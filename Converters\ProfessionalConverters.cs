using System;
using System.Globalization;
using System.Windows.Data;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// محول النص المشروط - يعرض نص مختلف حسب القيمة المنطقية
    /// </summary>
    public class BoolToTextConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue && parameter is string paramString)
            {
                var texts = paramString.Split('|');
                if (texts.Length >= 2)
                {
                    return boolValue ? texts[0] : texts[1];
                }
            }
            return value?.ToString() ?? "";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// محول الأيقونة المشروطة - يعرض أيقونة مختلفة حسب القيمة المنطقية
    /// </summary>
    public class BoolToIconConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue && parameter is string paramString)
            {
                var icons = paramString.Split(new string[] { "🏆" }, StringSplitOptions.None);
                if (icons.Length >= 2)
                {
                    return boolValue ? icons[0] : "🏆" + icons[1];
                }
            }
            return "🏆";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// محول حالة التحميل إلى نص
    /// </summary>
    public class LoadingStateConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isLoading)
            {
                return isLoading ? "⏳ جاري التحميل..." : "✅ جاهز";
            }
            return "جاهز";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// محول العدد إلى نص مع تنسيق احترافي
    /// </summary>
    public class NumberToFormattedTextConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is decimal decimalValue)
            {
                return decimalValue.ToString("N0", new CultureInfo("ar-SA")) + " ريال";
            }
            if (value is int intValue)
            {
                return intValue.ToString("N0", new CultureInfo("ar-SA"));
            }
            return value?.ToString() ?? "0";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// محول لعكس النص (من اليمين إلى اليسار)
    /// مفيد لأرقام المشاريع والأرقام المركبة
    /// </summary>
    public class ReverseTextConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string text && !string.IsNullOrEmpty(text))
            {
                // عكس النص حرف بحرف
                char[] charArray = text.ToCharArray();
                Array.Reverse(charArray);
                return new string(charArray);
            }
            return value?.ToString() ?? "";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // عكس العملية - إعادة عكس النص
            if (value is string text && !string.IsNullOrEmpty(text))
            {
                char[] charArray = text.ToCharArray();
                Array.Reverse(charArray);
                return new string(charArray);
            }
            return value?.ToString() ?? "";
        }
    }

    /// <summary>
    /// محول حالة العرض إلى لون
    /// </summary>
    public class OfferStatusToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isSelected)
            {
                return isSelected ? "#4CAF50" : "#9E9E9E";
            }
            return "#9E9E9E";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
