using System;
using System.IO;
using QuestPDF.Fluent;
using QuestPDF.Infrastructure;
using DriverManagementSystem.Models;
using QuestPDF.Helpers;
using System.Linq;

namespace DriverManagementSystem.Services
{
    public static class DriverAdjustmentPdfService
    {
        static DriverAdjustmentPdfService()
        {
            QuestPDF.Settings.License = LicenseType.Community;
            QuestPDF.Settings.EnableDebugging = true;
        }

        public static byte[] GenerateAdditionReport(FieldVisit visit, int additionDays, DateTime newReturnDate, string reason, decimal dailyRate)
        {
            var document = Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(0); // بدون هوامش لنتمكن من وضع الإطار كامل
                    page.DefaultTextStyle(x => x.FontFamily("Arial").FontSize(10).DirectionFromRightToLeft());

                    // إطار كامل حول محتوى الصفحة
                    page.Content().Border(2).BorderColor("#000000").Padding(15).Element(content =>
                    {
                        content.Column(column =>
                        {
                            // الهيدر مع الشعار والمعلومات
                            column.Item().Row(row =>
                            {
                                // اليسار: التاريخ ورقم الزيارة
                                row.RelativeItem().Column(col =>
                                {
                                    col.Item().AlignLeft().Text($"التاريخ: {DateTime.Now:dd/MM/yyyy}").FontSize(10);
                                    col.Item().AlignLeft().Text($"{visit.VisitNumber} :رقم الزيارة").FontSize(10);
                                });

                                // الشعار في المنتصف
                                row.ConstantItem(100).AlignCenter().Height(48)
                                    .Image(@"C:\Users\<USER>\Desktop\sys\Icons\sfd.png");

                                // اليمين: اسم الجهة
                                row.RelativeItem().Column(col =>
                                {
                                    col.Item().AlignRight().Text("الصندوق الاجتماعي للتنمية").FontSize(12).Bold();
                                    col.Item().AlignRight().Text("فرع ذمار والبيضاء").FontSize(10);
                                });
                            });

                            // العنوان أسفل الشعار مباشرة
                            column.Item().PaddingTop(8).PaddingBottom(2)
                                .BorderBottom(1).BorderColor("#000000")
                                .AlignCenter().Text("أمر تغيير مهمة ميدانية - إضافة أيام").FontSize(15).Bold();

                            // معلومات المشروع والسائق بدون فراغ مرتفع
                            column.Item().PaddingVertical(6).Element(c => CreateProfessionalProjectInfo(c, visit));

                            // تفاصيل الإضافة
                            column.Item().PaddingVertical(6).Element(c => CreateProfessionalAdditionDetails(c, visit, additionDays, newReturnDate, reason, dailyRate));

                            // التوقيعات
                            column.Item().PaddingVertical(8).Element(c => CreateProfessionalSignatures(c, visit));
                        });
                    });
                });
            });

            return document.GeneratePdf();
        }

        public static byte[] GenerateDeductionReport(FieldVisit visit, int deductionDays, DateTime newReturnDate, string reason, decimal dailyRate)
        {
            var document = Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(0);
                    page.DefaultTextStyle(x => x.FontFamily("Arial").FontSize(10).DirectionFromRightToLeft());

                    page.Content().Border(2).BorderColor("#000000").Padding(15).Element(content =>
                    {
                        content.Column(column =>
                        {
                            column.Item().Row(row =>
                            {
                                row.RelativeItem().Column(col =>
                                {
                                    col.Item().AlignLeft().Text($"التاريخ: {DateTime.Now:dd/MM/yyyy}").FontSize(10);
                                    col.Item().AlignLeft().Text($"{visit.VisitNumber} :رقم الزيارة").FontSize(10);
                                });

                                row.ConstantItem(100).AlignCenter().Height(48)
                                    .Image(@"C:\Users\<USER>\Desktop\sys\Icons\sfd.png");

                                row.RelativeItem().Column(col =>
                                {
                                    col.Item().AlignRight().Text("الصندوق الاجتماعي للتنمية").FontSize(12).Bold();
                                    col.Item().AlignRight().Text("فرع ذمار والبيضاء").FontSize(10);
                                });
                            });

                            column.Item().PaddingTop(8).PaddingBottom(2)
                                .BorderBottom(1).BorderColor("#000000")
                                .AlignCenter().Text("أمر تغيير مهمة ميدانية - خصم أيام").FontSize(15).Bold();

                            column.Item().PaddingVertical(6).Element(c => CreateProfessionalProjectInfo(c, visit));

                            // تفاصيل الخصم (مماثلة للإضافة مع التعديلات الخاصة)
                            column.Item().PaddingVertical(6).Element(c => CreateProfessionalDeductionDetails(c, visit, deductionDays, newReturnDate, reason, dailyRate));

                            column.Item().PaddingVertical(8).Element(c => CreateProfessionalSignatures(c, visit));
                        });
                    });
                });
            });

            return document.GeneratePdf();
        }

        /// <summary>
        /// إنشاء قسم معلومات المشروع والسائق بشكل احترافي مع تنسيق أفقي واتجاه صحيح
        /// </summary>
        private static void CreateProfessionalProjectInfo(IContainer container, FieldVisit visit)
        {
            container.Column(column =>
            {
                // مسافة صغيرة من الأعلى
                column.Item().PaddingVertical(4);

                // قسم معلومات المشروع
                column.Item().Border(1).BorderColor("#000000").Column(projectCol =>
                {
                    // عنوان القسم
                    projectCol.Item().Background("#E8E8E8")
                        .Padding(5)
                        .AlignCenter()
                        .Text("معلومات المشروع")
                        .FontSize(11)
                        .Bold()
                        .DirectionFromRightToLeft();

                    // محتوى رقم واسم المشروع
                    projectCol.Item().Padding(6).Column(content =>
                    {
                        // رقم المشروع
                        content.Item().Row(row =>
                        {
                            row.ConstantItem(100).AlignLeft().Text("رقم المشروع :")
                                .FontSize(10).Bold().DirectionFromRightToLeft();
                            row.RelativeItem().AlignLeft().Text(visit.Projects?.FirstOrDefault()?.ProjectNumber ?? "---")
                                .FontSize(10).DirectionFromRightToLeft();
                        });

                        // مسافة صغيرة بين السطور
                        content.Item().PaddingVertical(2);
                        // مسافة صغيرة بين السطور
                        content.Item().PaddingVertical(2);

                        // اسم المشروع - سطر كامل
                        content.Item().AlignRight().Text(text =>
                        {
                            text.Span("اسم المشروع : ")
                                .FontSize(10)
                                .Bold()
                                .DirectionFromRightToLeft();

                            text.Span(visit.Projects?.FirstOrDefault()?.ProjectName ?? "---")
                                .FontSize(10)
                                .DirectionFromRightToLeft();
                        });
                    });
                });

                // مسافة بين القسمين
                column.Item().PaddingVertical(6);

                // قسم معلومات السائق
                column.Item().Border(1).BorderColor("#000000").Column(driverCol =>
                {
                    // عنوان القسم
                    driverCol.Item().Background("#E8E8E8")
                        .Padding(5)
                        .AlignCenter()
                        .Text("معلومات السائق")
                        .FontSize(11)
                        .Bold()
                        .DirectionFromRightToLeft();

                    // محتوى اسم السائق ورقم الهاتف
                    driverCol.Item().Padding(6).Column(content =>
                    {
                        // اسم السائق - سطر كامل
                        content.Item().AlignRight().Text(text =>
                        {
                            text.Span("اسم السائق : ").FontSize(10).Bold().DirectionFromRightToLeft();
                            text.Span(ExtractWinnerDriverName(visit) ?? "---").FontSize(10).DirectionFromRightToLeft();
                        });

                        // مسافة صغيرة
                        content.Item().PaddingVertical(2);

                        // رقم التلفون - سطر كامل
                        content.Item().AlignRight().Text(text =>
                        {
                            text.Span("رقم التلفون : ").FontSize(10).Bold().DirectionFromRightToLeft();
                            text.Span(ExtractWinnerDriverPhone(visit) ?? "---").FontSize(10).DirectionFromRightToLeft();
                        });
                    });
                });
            });
        }
        private static void CreateProfessionalAdditionDetails(IContainer container, FieldVisit visit, int additionDays, DateTime newReturnDate, string reason, decimal dailyRate)
        {
            var originalAmount = ExtractWinnerAmount(visit);
            var additionalAmount = additionDays * dailyRate;
            var totalAmount = originalAmount + additionalAmount;

            container.Column(column =>
            {
                column.Item().PaddingVertical(2);

                column.Item().Border(1).BorderColor("#000000").Column(col =>
                {
                    col.Item().Background("#E8E8E8").Padding(5).AlignCenter()
                        .Text("تفاصيل الإضافة").FontSize(11).Bold().DirectionFromRightToLeft();

                    col.Item().Padding(5).Table(table =>
                    {
                        table.ColumnsDefinition(columns =>
                        {
                            columns.RelativeColumn(1);
                            columns.RelativeColumn(1);
                        });

                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("تاريخ العودة الأصلي:").FontSize(9).Bold().DirectionFromRightToLeft();
                            cell.Item().Text(visit.ReturnDate.ToString("dd/MM/yyyy")).FontSize(10).DirectionFromRightToLeft();
                        });

                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("تاريخ العودة الجديد:").FontSize(9).Bold().DirectionFromRightToLeft();
                            cell.Item().Text(newReturnDate.ToString("dd/MM/yyyy")).FontSize(10).DirectionFromRightToLeft();
                        });

                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("عدد الأيام المضافة:").FontSize(9).Bold().DirectionFromRightToLeft();
                            cell.Item().Text($"{additionDays} يوم").FontSize(10).DirectionFromRightToLeft();
                        });

                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("المبلغ الأصلي:").FontSize(9).Bold().DirectionFromRightToLeft();
                            cell.Item().Text($"{originalAmount:N0} ريال").FontSize(10).DirectionFromRightToLeft();
                        });

                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("مبلغ الإضافة:").FontSize(9).Bold().DirectionFromRightToLeft();
                            cell.Item().Text($"{additionalAmount:N0} ريال").FontSize(10).DirectionFromRightToLeft();
                        });

                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("المبلغ الإجمالي:").FontSize(9).Bold().DirectionFromRightToLeft();
                            cell.Item().Text($"{totalAmount:N0} ريال").FontSize(11).Bold().DirectionFromRightToLeft();
                        });
                    });
                });

                column.Item().PaddingVertical(1);

                column.Item().Border(1).BorderColor("#000000").Column(col =>
                {
                    col.Item().Background("#E8E8E8").Padding(5).AlignCenter()
                        .Text("السبب").FontSize(11).Bold().DirectionFromRightToLeft();

                    col.Item().Padding(5).MinHeight(40).AlignRight()
                        .Text(reason).FontSize(10).DirectionFromRightToLeft();
                });
            });
        }

        private static void CreateProfessionalDeductionDetails(IContainer container, FieldVisit visit, int deductionDays, DateTime newReturnDate, string reason, decimal dailyRate)
        {
            var originalAmount = ExtractWinnerAmount(visit);
            var deductionAmount = deductionDays * dailyRate;
            var totalAmount = originalAmount - deductionAmount;

            container.Column(column =>
            {
                column.Item().PaddingVertical(2);

                column.Item().Border(1).BorderColor("#000000").Column(col =>
                {
                    col.Item().Background("#E8E8E8").Padding(5).AlignCenter()
                        .Text("تفاصيل الخصم").FontSize(11).Bold().DirectionFromRightToLeft();

                    col.Item().Padding(5).Table(table =>
                    {
                        table.ColumnsDefinition(columns =>
                        {
                            columns.RelativeColumn(1);
                            columns.RelativeColumn(1);
                        });

                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("تاريخ العودة الأصلي:").FontSize(9).Bold().DirectionFromRightToLeft();
                            cell.Item().Text(visit.ReturnDate.ToString("dd/MM/yyyy")).FontSize(10).DirectionFromRightToLeft();
                        });

                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("تاريخ العودة الجديد:").FontSize(9).Bold().DirectionFromRightToLeft();
                            cell.Item().Text(newReturnDate.ToString("dd/MM/yyyy")).FontSize(10).DirectionFromRightToLeft();
                        });

                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("عدد الأيام المخصومة:").FontSize(9).Bold().DirectionFromRightToLeft();
                            cell.Item().Text($"{deductionDays} يوم").FontSize(10).DirectionFromRightToLeft();
                        });

                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("المبلغ الأصلي:").FontSize(9).Bold().DirectionFromRightToLeft();
                            cell.Item().Text($"{originalAmount:N0} ريال").FontSize(10).DirectionFromRightToLeft();
                        });

                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("مبلغ الخصم:").FontSize(9).Bold().DirectionFromRightToLeft();
                            cell.Item().Text($"{deductionAmount:N0} ريال").FontSize(10).DirectionFromRightToLeft();
                        });

                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("المبلغ بعد الخصم:").FontSize(9).Bold().DirectionFromRightToLeft();
                            cell.Item().Text($"{totalAmount:N0} ريال").FontSize(11).Bold().DirectionFromRightToLeft();
                        });
                    });
                });

                column.Item().PaddingVertical(1);

                column.Item().Border(1).BorderColor("#000000").Column(col =>
                {
                    col.Item().Background("#E8E8E8").Padding(5).AlignCenter()
                        .Text("السبب").FontSize(11).Bold().DirectionFromRightToLeft();

                    col.Item().Padding(5).MinHeight(40).AlignRight()
                        .Text(reason).FontSize(10).DirectionFromRightToLeft();
                });
            });
        }

        private static void CreateProfessionalSignatures(IContainer container, FieldVisit visit)
        {
            container.Column(column =>
            {
                column.Item().PaddingVertical(10);

                column.Item().Border(1).BorderColor("#000000").Column(col =>
                {
                    col.Item().Background("#E8E8E8").Padding(5).AlignCenter()
                        .Text("التوقيعات").FontSize(11).Bold().DirectionFromRightToLeft();

                    col.Item().Padding(8).Table(table =>
                    {
                        table.ColumnsDefinition(columns =>
                        {
                            columns.RelativeColumn(1);
                            columns.RelativeColumn(1);
                        });

                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("السائق:").FontSize(10).Bold().DirectionFromRightToLeft();
                            cell.Item().PaddingVertical(2);
                            cell.Item().Text($"الاسم: {ExtractWinnerDriverName(visit) ?? "---"}").FontSize(9).DirectionFromRightToLeft();
                            cell.Item().Text($"رقم التلفون: {ExtractWinnerDriverPhone(visit) ?? "---"}").FontSize(9).DirectionFromRightToLeft();
                            cell.Item().PaddingVertical(15);
                            cell.Item().LineHorizontal(1).LineColor("#000000");
                            cell.Item().AlignCenter().Text("التوقيع").FontSize(9).DirectionFromRightToLeft();
                        });

                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("القائم بالمهمة:").FontSize(10).Bold().DirectionFromRightToLeft();
                            cell.Item().PaddingVertical(2);
                            cell.Item().Text("الاسم: علي أحمد العبدي").FontSize(9).DirectionFromRightToLeft();
                            cell.Item().Text("المنصب: سكرتاريه التغذية").FontSize(9).DirectionFromRightToLeft();
                            cell.Item().PaddingVertical(15);
                            cell.Item().LineHorizontal(1).LineColor("#000000");
                            cell.Item().AlignCenter().Text("التوقيع").FontSize(9).DirectionFromRightToLeft();
                        });
                    });
                });

                column.Item().PaddingVertical(1);

                column.Item().Border(1).BorderColor("#000000").Column(col =>
                {
                    col.Item().Background("#E8E8E8").Padding(5).AlignCenter()
                        .Text("اعتماد مدير الفرع").FontSize(11).Bold().DirectionFromRightToLeft();

                    col.Item().Padding(8).Column(content =>
                    {
                        content.Item().AlignCenter().Text("م/محمد محمد الديلمي").FontSize(11).DirectionFromRightToLeft();
                        content.Item().PaddingVertical(20);
                        content.Item().LineHorizontal(1).LineColor("#000000");
                        content.Item().AlignCenter().Text("التوقيع والختم").FontSize(9).DirectionFromRightToLeft();
                    });
                });
            });
        }

        public static void SaveAndOpenReport(byte[] pdfBytes, string fileName)
        {
            try
            {
                var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                var fullPath = Path.Combine(documentsPath, fileName + ".pdf");

                File.WriteAllBytes(fullPath, pdfBytes);

                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo()
                {
                    FileName = fullPath,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في حفظ أو فتح التقرير: " + ex.Message);
            }
        }

        // ==========================
        // دوال استخراج البيانات (كما هي في كودك الحالي)
        // ==========================
        private static string ExtractWinnerDriverName(FieldVisit visit)
        {
            try
            {
                if (!string.IsNullOrEmpty(visit.SelectedDrivers))
                {
                    var driversData = visit.SelectedDrivers.Split(new char[] { '\n', '|' }, StringSplitOptions.RemoveEmptyEntries);

                    foreach (var driverData in driversData)
                    {
                        var parts = driverData.Split(" - ");
                        if (parts.Length >= 3 && (parts[2].Contains("🏆 فائز") || parts[2].Contains("فائز")))
                        {
                            return parts[0].Trim();
                        }
                    }
                }

                if (!string.IsNullOrEmpty(visit.DriverContract))
                    return visit.DriverContract;

                return "";
            }
            catch
            {
                return "";
            }
        }

        private static decimal ExtractWinnerAmount(FieldVisit visit)
        {
            try
            {
                if (!string.IsNullOrEmpty(visit.SelectedDrivers))
                {
                    var driversData = visit.SelectedDrivers.Split(new char[] { '\n', '|' }, StringSplitOptions.RemoveEmptyEntries);

                    foreach (var driverData in driversData)
                    {
                        var parts = driverData.Split(" - ");
                        if (parts.Length >= 3 && (parts[2].Contains("🏆 فائز") || parts[2].Contains("فائز")))
                        {
                            if (parts.Length >= 2)
                            {
                                var amountText = parts[1].Replace(" ريال", "").Replace(",", "").Trim();
                                if (decimal.TryParse(amountText, out decimal amount))
                                    return amount;
                            }
                        }
                    }
                }

                return 54000; // قيمة افتراضية
            }
            catch
            {
                return 54000;
            }
        }

        private static string ExtractWinnerDriverPhone(FieldVisit visit)
        {
            try
            {
                if (!string.IsNullOrEmpty(visit.SelectedDrivers))
                {
                    var driversData = visit.SelectedDrivers.Split(new char[] { '\n', '|' }, StringSplitOptions.RemoveEmptyEntries);

                    foreach (var driverData in driversData)
                    {
                        var parts = driverData.Split(" - ");
                        if (parts.Length >= 3 && (parts[2].Contains("🏆 فائز") || parts[2].Contains("فائز")))
                        {
                            if (parts.Length >= 2)
                                return parts[1].Trim();
                        }
                    }
                }

                return "777123456"; // رقم افتراضي
            }
            catch
            {
                return "777123456";
            }
        }
    }
}
