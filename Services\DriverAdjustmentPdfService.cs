using System;
using System.IO;
using System.Linq;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة إنتاج تقارير PDF للخصم والإضافة باستخدام QuestPDF
    /// </summary>
    public static class DriverAdjustmentPdfService
    {
        static DriverAdjustmentPdfService()
        {
            // تفعيل QuestPDF للاستخدام المجاني
            QuestPDF.Settings.License = LicenseType.Community;
            // تفعيل إعدادات التصحيح لحل مشاكل التخطيط
            QuestPDF.Settings.EnableDebugging = true;
        }

        /// <summary>
        /// إنشاء تقرير PDF للإضافة
        /// </summary>
        public static byte[] GenerateAdditionReport(FieldVisit visit, int additionDays, DateTime newReturnDate, string reason, decimal dailyRate)
        {
            var document = Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(10); // تقليل الهامش
                    page.DefaultTextStyle(x => x.FontFamily("Arial").FontSize(10).DirectionFromRightToLeft()); // تقليل حجم الخط

                    page.Content().Element(content => CreateAdditionContent(content, visit, additionDays, newReturnDate, reason, dailyRate));
                });
            });

            return document.GeneratePdf();
        }

        /// <summary>
        /// إنشاء تقرير PDF للخصم
        /// </summary>
        public static byte[] GenerateDeductionReport(FieldVisit visit, int deductionDays, DateTime newReturnDate, string reason, decimal dailyRate)
        {
            var document = Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(10); // تقليل الهامش
                    page.DefaultTextStyle(x => x.FontFamily("Arial").FontSize(10).DirectionFromRightToLeft()); // تقليل حجم الخط

                    page.Content().Element(content => CreateDeductionContent(content, visit, deductionDays, newReturnDate, reason, dailyRate));
                });
            });

            return document.GeneratePdf();
        }

        /// <summary>
        /// إنشاء محتوى تقرير الإضافة
        /// </summary>
        private static void CreateAdditionContent(IContainer container, FieldVisit visit, int additionDays, DateTime newReturnDate, string reason, decimal dailyRate)
        {
            container.DefaultTextStyle(x => x.FontFamily("Arial").DirectionFromRightToLeft()).Column(column =>
            {
                // الهيدر الاحترافي
                column.Item().Element(c => CreateProfessionalHeader(c, "أمر تغيير مهمة ميدانية - إضافة أيام", visit));

                column.Item().PaddingVertical(15);

                // معلومات المشروع والسائق
                column.Item().Element(c => CreateProfessionalProjectInfo(c, visit));

                column.Item().PaddingVertical(15);

                // تفاصيل الإضافة
                column.Item().Element(c => CreateProfessionalAdditionDetails(c, visit, additionDays, newReturnDate, reason, dailyRate));

                column.Item().PaddingVertical(20);

                // التوقيعات
                column.Item().Element(c => CreateProfessionalSignatures(c, visit));
            });
        }

        /// <summary>
        /// إنشاء محتوى تقرير الخصم
        /// </summary>
        private static void CreateDeductionContent(IContainer container, FieldVisit visit, int deductionDays, DateTime newReturnDate, string reason, decimal dailyRate)
        {
            container.DefaultTextStyle(x => x.FontFamily("Arial").DirectionFromRightToLeft()).Column(column =>
            {
                // الهيدر الاحترافي
                column.Item().Element(c => CreateProfessionalHeader(c, "أمر تغيير مهمة ميدانية - خصم أيام", visit));

                column.Item().PaddingVertical(15);

                // معلومات المشروع والسائق
                column.Item().Element(c => CreateProfessionalProjectInfo(c, visit));

                column.Item().PaddingVertical(15);

                // تفاصيل الخصم
                column.Item().Element(c => CreateProfessionalDeductionDetails(c, visit, deductionDays, newReturnDate, reason, dailyRate));

                column.Item().PaddingVertical(20);

                // التوقيعات
                column.Item().Element(c => CreateProfessionalSignatures(c, visit));
            });
        }

        /// <summary>
        /// إنشاء الهيدر الاحترافي للطباعة
        /// </summary>
        private static void CreateProfessionalHeader(IContainer container, string title, FieldVisit visit)
        {
            container.Column(column =>
            {
                // الهيدر العلوي منظم
                column.Item().Border(1).BorderColor("#000000").Padding(5).Row(row =>
                {
                    // التاريخ ورقم المستند (يسار)
                    row.RelativeItem().Column(col =>
                    {
                        col.Item().AlignLeft().Text($"التاريخ: {DateTime.Now:dd/MM/yyyy}")
                            .FontSize(10).DirectionFromRightToLeft();
                        col.Item().AlignLeft().Text($"{visit.VisitNumber} :رقم الزيارة")
                            .FontSize(10).DirectionFromRightToLeft();
                    });

                    // الشعار (وسط)
                    row.ConstantItem(100).Column(col =>
                    {
                        try
                        {
                            var logoPath = @"C:\Users\<USER>\Desktop\sys\Icons\sfd.png";
                            if (System.IO.File.Exists(logoPath))
                            {
                                col.Item().AlignCenter().Height(50).Image(logoPath);
                            }
                            else
                            {
                                col.Item().AlignCenter().Text("🏛️").FontSize(30);
                            }
                        }
                        catch
                        {
                            col.Item().AlignCenter().Text("🏛️").FontSize(30);
                        }
                    });

                    // المؤسسة (يمين)
                    row.RelativeItem().Column(col =>
                    {
                        col.Item().AlignRight().Text("الصندوق الاجتماعي للتنمية")
                            .FontSize(12).Bold().DirectionFromRightToLeft();
                        col.Item().AlignRight().Text("فرع ذمار والبيضاء")
                            .FontSize(10).DirectionFromRightToLeft();
                    });
                });

                column.Item().PaddingVertical(2);

                // العنوان الرئيسي
                column.Item()
                    .Border(1)
                    .BorderColor("#000000")
                    .Padding(5)
                    .AlignCenter()
                    .Text($"{title}")
                    .FontSize(14)
                    .Bold()
                    .DirectionFromRightToLeft();
            });
        }


        /// <summary>
        /// إنشاء معلومات المشروع الاحترافية
        /// </summary>
        private static void CreateProjectInfo(IContainer container, FieldVisit visit)
        {
            container.Padding(3).Column(column =>
            {
                // معلومات المشروع مبسطة
                column.Item().Border(1).BorderColor("#000000").Padding(3).Column(col =>
                {
                    col.Item().Row(row =>
                    {
                        row.RelativeItem().AlignRight()
                            .Text($"رقم المشروع: {visit.Projects?.FirstOrDefault()?.ProjectNumber ?? "911-13916"}")
                            .FontSize(8).Bold().DirectionFromRightToLeft();

                        row.RelativeItem().AlignLeft()
                            .Text($"السائق: {ExtractWinnerDriverName(visit) ?? "زياد سعيد أحمد حسن السامري"}")
                            .FontSize(8).Bold().DirectionFromRightToLeft();
                    });

                    col.Item().PaddingVertical(1);

                    col.Item().AlignRight()
                        .Text($"اسم المشروع: {visit.Projects?.FirstOrDefault()?.ProjectName ?? "استكمال المرحلة الثالثة من أنشطة التحويلات النقدية المشروطة"}")
                        .FontSize(7).DirectionFromRightToLeft();

                });
            });
        }

        /// <summary>
        /// إنشاء قسم معلومات المشروع الاحترافي للطباعة
        /// </summary>
        private static void CreateProfessionalProjectInfo(IContainer container, FieldVisit visit)
        {
            container.Column(column =>
            {
                column.Item().PaddingVertical(2);

                // قسم معلومات المشروع
                column.Item().Border(1).BorderColor("#000000").Column(col =>
                {
                    // عنوان القسم
                    col.Item().Background("#E8E8E8").Padding(5).AlignCenter()
                        .Text("معلومات المشروع").FontSize(11).Bold().DirectionFromRightToLeft();

                    // محتوى المشروع
                    col.Item().Padding(5).Column(content =>
                    {
                        content.Item().Row(row =>
                        {
                            row.ConstantItem(80).AlignRight().Text("رقم المشروع:").FontSize(10).Bold().DirectionFromRightToLeft();
                            row.RelativeItem().AlignRight().Text(visit.Projects?.FirstOrDefault()?.ProjectNumber ?? "911-13916")
                                .FontSize(10).DirectionFromRightToLeft();
                        });

                        content.Item().PaddingVertical(1);

                        content.Item().Row(row =>
                        {
                            row.ConstantItem(80).AlignRight().Text("اسم المشروع:").FontSize(10).Bold().DirectionFromRightToLeft();
                            row.RelativeItem().AlignRight().Text(visit.Projects?.FirstOrDefault()?.ProjectName ?? "استكمال المرحلة الثالثة من أنشطة التحويلات النقدية المشروطة")
                                .FontSize(10).DirectionFromRightToLeft();
                        });
                    });
                });

                column.Item().PaddingVertical(1);

                // قسم معلومات السائق
                column.Item().Border(1).BorderColor("#000000").Column(col =>
                {
                    // عنوان القسم
                    col.Item().Background("#E8E8E8").Padding(5).AlignCenter()
                        .Text("معلومات السائق").FontSize(11).Bold().DirectionFromRightToLeft();

                    // محتوى السائق
                    col.Item().Padding(5).Column(content =>
                    {
                        content.Item().Row(row =>
                        {
                            row.ConstantItem(80).AlignRight().Text("اسم السائق:").FontSize(10).Bold().DirectionFromRightToLeft();
                            row.RelativeItem().AlignRight().Text(ExtractWinnerDriverName(visit) ?? "زياد سعيد أحمد حسن السامري")
                                .FontSize(10).DirectionFromRightToLeft();
                        });

                        content.Item().PaddingVertical(1);

                        content.Item().Row(row =>
                        {
                            row.ConstantItem(80).AlignRight().Text("رقم التلفون:").FontSize(10).Bold().DirectionFromRightToLeft();
                            row.RelativeItem().AlignRight().Text(ExtractWinnerDriverPhone(visit) ?? "777123456")
                                .FontSize(10).DirectionFromRightToLeft();
                        });
                    });
                });
            });
        }

        /// <summary>
        /// إنشاء تفاصيل الإضافة الاحترافية للطباعة
        /// </summary>
        private static void CreateProfessionalAdditionDetails(IContainer container, FieldVisit visit, int additionDays, DateTime newReturnDate, string reason, decimal dailyRate)
        {
            // استخراج المبلغ الأصلي من بيانات السائق الفائز
            var originalAmount = ExtractWinnerAmount(visit);
            var additionalAmount = additionDays * dailyRate;
            var totalAmount = originalAmount + additionalAmount;

            container.Column(column =>
            {
                column.Item().PaddingVertical(2);

                // قسم تفاصيل الإضافة
                column.Item().Border(1).BorderColor("#000000").Column(col =>
                {
                    // عنوان القسم
                    col.Item().Background("#E8E8E8").Padding(5).AlignCenter()
                        .Text("تفاصيل الإضافة").FontSize(11).Bold().DirectionFromRightToLeft();

                    // محتوى التفاصيل
                    col.Item().Padding(5).Table(table =>
                    {
                        table.ColumnsDefinition(columns =>
                        {
                            columns.RelativeColumn(1);
                            columns.RelativeColumn(1);
                        });

                        // الصف الأول: التواريخ
                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("تاريخ العودة الأصلي:").FontSize(9).Bold().DirectionFromRightToLeft();
                            cell.Item().Text(visit.ReturnDate.ToString("dd/MM/yyyy")).FontSize(10).DirectionFromRightToLeft();
                        });

                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("تاريخ العودة الجديد:").FontSize(9).Bold().DirectionFromRightToLeft();
                            cell.Item().Text(newReturnDate.ToString("dd/MM/yyyy")).FontSize(10).DirectionFromRightToLeft();
                        });

                        // الصف الثاني: الأيام والمبلغ الأصلي
                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("عدد الأيام المضافة:").FontSize(9).Bold().DirectionFromRightToLeft();
                            cell.Item().Text($"{additionDays} يوم").FontSize(10).DirectionFromRightToLeft();
                        });

                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("المبلغ الأصلي:").FontSize(9).Bold().DirectionFromRightToLeft();
                            cell.Item().Text($"{originalAmount:N0} ريال").FontSize(10).DirectionFromRightToLeft();
                        });

                        // الصف الثالث: مبلغ الإضافة والمبلغ الإجمالي
                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("مبلغ الإضافة:").FontSize(9).Bold().DirectionFromRightToLeft();
                            cell.Item().Text($"{additionalAmount:N0} ريال").FontSize(10).DirectionFromRightToLeft();
                        });

                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("المبلغ الإجمالي:").FontSize(9).Bold().DirectionFromRightToLeft();
                            cell.Item().Text($"{totalAmount:N0} ريال").FontSize(11).Bold().DirectionFromRightToLeft();
                        });
                    });
                });

                column.Item().PaddingVertical(1);

                // قسم السبب
                column.Item().Border(1).BorderColor("#000000").Column(col =>
                {
                    // عنوان القسم
                    col.Item().Background("#E8E8E8").Padding(5).AlignCenter()
                        .Text("السبب").FontSize(11).Bold().DirectionFromRightToLeft();

                    // محتوى السبب
                    col.Item().Padding(5).MinHeight(40).AlignRight()
                        .Text(reason).FontSize(10).DirectionFromRightToLeft();
                });
            });
        }

        /// <summary>
        /// إنشاء تفاصيل الإضافة الاحترافية
        /// </summary>
        private static void CreateAdditionDetails(IContainer container, FieldVisit visit, int additionDays, DateTime newReturnDate, string reason, decimal dailyRate)
        {
            // استخراج المبلغ الأصلي من بيانات السائق الفائز
            var originalAmount = ExtractWinnerAmount(visit);
            var additionalAmount = additionDays * dailyRate;
            var totalAmount = originalAmount + additionalAmount;

            container.Padding(10).Column(column =>
            {
                // عنوان قسم تفاصيل الإضافة
                column.Item().Background("#27AE60").Padding(10).Row(row =>
                {
                    row.RelativeItem().Text("➕ تفاصيل اضافة ايام للزيارة").FontSize(14).Bold().FontColor("#FFFFFF");
                });

                // بطاقة تفاصيل السائق والمهمة
                column.Item().Border(1).BorderColor("#BDC3C7").Background("#F8F9FA").Padding(15).Column(col =>
                {
                    // معلومات السائق
                    col.Item().Row(row =>
                    {
                        row.RelativeItem().Column(c =>
                        {
                            c.Item().AlignRight().Text("🔄 تاريخ العودة الجديد").FontSize(12).Bold().FontColor("#2C3E50").DirectionFromRightToLeft();
                            c.Item().AlignRight().Text(newReturnDate.ToString("dd/MM/yyyy")).FontSize(11).FontColor("#34495E").DirectionFromRightToLeft();
                        });

                        row.RelativeItem().Column(c =>
                        {
                            c.Item().AlignRight().Text("📅 تاريخ العودة للزيارة").FontSize(12).Bold().FontColor("#2C3E50").DirectionFromRightToLeft();
                            c.Item().AlignRight().Text(visit.DepartureDate.ToString("dd/MM/yyyy")).FontSize(11).FontColor("#34495E").DirectionFromRightToLeft();
                        });

                        row.RelativeItem().Column(c =>
                        {
                            c.Item().AlignRight().Text("👨‍💼 السائق").FontSize(12).Bold().FontColor("#2C3E50").DirectionFromRightToLeft();
                            c.Item().AlignRight().Text(ExtractWinnerDriverName(visit)).FontSize(11).FontColor("#34495E").DirectionFromRightToLeft();
                        });
                    });

                    col.Item().PaddingVertical(10);

                    // المبالغ المالية
                    col.Item().Background("#ECF0F1").Padding(10).Row(row =>
                    {
                        row.RelativeItem().Column(c =>
                        {
                            c.Item().AlignRight().Text("💎 استحقاق السائق بعد الاضافة").FontSize(12).Bold().FontColor("#8E44AD").DirectionFromRightToLeft();
                            c.Item().AlignRight().Text($"{totalAmount:N0} ريال").FontSize(11).FontColor("#8E44AD").DirectionFromRightToLeft();
                        });

                        row.RelativeItem().Column(c =>
                        {
                            c.Item().AlignRight().Text("➕ مبلغ الإضافة").FontSize(12).Bold().FontColor("#27AE60").DirectionFromRightToLeft();
                            c.Item().AlignRight().Text($"{additionalAmount:N0} ريال").FontSize(11).FontColor("#27AE60").DirectionFromRightToLeft();
                        });

                        row.RelativeItem().Column(c =>
                        {
                            c.Item().AlignRight().Text("💰 المبلغ المقدم من السائق").FontSize(12).Bold().FontColor("#2C3E50").DirectionFromRightToLeft();
                            c.Item().AlignRight().Text($"{originalAmount:N0} ريال").FontSize(11).FontColor("#34495E").DirectionFromRightToLeft();
                        });
                    });
                });

                column.Item().PaddingVertical(10);

                // قسم السبب والملاحظات
                column.Item().Background("#F39C12").Padding(10).Row(row =>
                {
                    row.RelativeItem().Text("📝 السبب والملاحظات").FontSize(14).Bold().FontColor("#FFFFFF");
                });

                column.Item().Border(1).BorderColor("#BDC3C7").Background("#FEF9E7").Padding(15).Column(col =>
                {
                    // سبب الإضافة
                    col.Item().Column(c =>
                    {
                        c.Item().Text("📋 سبب الإضافة:").FontSize(12).Bold().FontColor("#2C3E50");
                        c.Item().PaddingVertical(5);
                        c.Item().Border(1).BorderColor("#BDC3C7").Background("#FFFFFF").Padding(10).MinHeight(60)
                            .Text(reason).FontSize(11).FontColor("#34495E");
                    });

                    col.Item().PaddingVertical(10);

                    // الملاحظات
                    col.Item().Column(c =>
                    {
                        c.Item().Text("💬 ملاحظات إضافية:").FontSize(12).Bold().FontColor("#2C3E50");
                        c.Item().PaddingVertical(5);
                        c.Item().Border(1).BorderColor("#BDC3C7").Background("#FFFFFF").Padding(10).MinHeight(40)
                            .Text("لا توجد ملاحظات إضافية").FontSize(11).FontColor("#7F8C8D");
                    });
                });

                column.Item().PaddingVertical(10);

                // جدول الحسابات
                column.Item().Row(row =>
                {
                    // الإضافة
                    row.RelativeItem().Border(1).Table(table =>
                    {
                        table.ColumnsDefinition(columns =>
                        {
                            columns.RelativeColumn(1);
                            columns.RelativeColumn(1);
                        });

                        table.Cell().Border(1).Padding(5).AlignCenter().Text("إضافة").FontSize(12).Bold();
                        table.Cell().Border(1).Padding(5).AlignCenter().Text("العدد الإضافية").FontSize(10).Bold();
                        table.Cell().Border(1).Padding(5).AlignCenter().Text($"{additionDays}").FontSize(10);

                        table.Cell().Border(1).Padding(5).AlignCenter().Text("تاريخ انتهاء المدة").FontSize(10).Bold();
                        table.Cell().Border(1).Padding(5).AlignCenter().Text(newReturnDate.ToString("dd/MM/yyyy")).FontSize(10);

                        table.Cell().Border(1).Padding(5).AlignCenter().Text("المبلغ المضاف للسائق").FontSize(10).Bold();
                        table.Cell().Border(1).Padding(5).AlignCenter().Text($"{additionalAmount:N0}").FontSize(10);

                        table.Cell().Border(1).Padding(5).AlignCenter().Text("الأجر اليومي للسائق").FontSize(10).Bold();
                        table.Cell().Border(1).Padding(5).AlignCenter().Text($"{dailyRate:N0}").FontSize(10);

                        table.Cell().Border(1).Padding(5).AlignCenter().Text("المدة كاملة للسفر").FontSize(10).Bold();
                        table.Cell().Border(1).Padding(5).AlignCenter().Text("3").FontSize(10);

                        table.Cell().Border(1).Padding(5).AlignCenter().Text("مستحقات السائق").FontSize(12).Bold();
                        table.Cell().Border(1).Padding(5).AlignCenter().Text($"{totalAmount:N0}").FontSize(14).Bold();
                    });

                    // الخصم (فارغ في حالة الإضافة)
                    row.RelativeItem().Border(1).Table(table =>
                    {
                        table.ColumnsDefinition(columns =>
                        {
                            columns.RelativeColumn(1);
                            columns.RelativeColumn(1);
                        });

                        table.Cell().Border(1).Padding(5).AlignCenter().Text("خصم").FontSize(12).Bold();
                        table.Cell().Border(1).Padding(5).AlignCenter().Text("عدد الأيام").FontSize(10).Bold();
                        table.Cell().Border(1).Padding(5).AlignCenter().Text("---").FontSize(10);

                        table.Cell().Border(1).Padding(5).AlignCenter().Text("مبلغ الخصم").FontSize(10).Bold();
                        table.Cell().Border(1).Padding(5).AlignCenter().Text("---").FontSize(10);

                        table.Cell().Border(1).Padding(5).AlignCenter().Text("ملاحظات").FontSize(10).Bold();
                        table.Cell().Border(1).Padding(20).AlignCenter().Text("---").FontSize(10);

                        table.Cell().Border(1).Padding(5).AlignCenter().Text("مستحقات السائق").FontSize(12).Bold();
                        table.Cell().Border(1).Padding(5).AlignCenter().Text("---").FontSize(14).Bold();
                    });
                });
            });
        }

        /// <summary>
        /// إنشاء التوقيعات الاحترافية للطباعة
        /// </summary>
        private static void CreateProfessionalSignatures(IContainer container, FieldVisit visit)
        {
            container.Column(column =>
            {
                column.Item().PaddingVertical(10);

                column.Item().PaddingVertical(2);

                // قسم التوقيعات
                column.Item().Border(1).BorderColor("#000000").Column(col =>
                {
                    // عنوان القسم
                    col.Item().Background("#E8E8E8").Padding(5).AlignCenter()
                        .Text("التوقيعات").FontSize(11).Bold().DirectionFromRightToLeft();

                    // محتوى التوقيعات
                    col.Item().Padding(8).Table(table =>
                    {
                        table.ColumnsDefinition(columns =>
                        {
                            columns.RelativeColumn(1); // السائق
                            columns.RelativeColumn(1); // القائم بالمهمة
                        });

                        // السائق
                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("السائق:").FontSize(10).Bold().DirectionFromRightToLeft();
                            cell.Item().PaddingVertical(2);
                            cell.Item().Text($"الاسم: {ExtractWinnerDriverName(visit) ?? "زياد سعيد أحمد حسن السامري"}").FontSize(9).DirectionFromRightToLeft();
                            cell.Item().Text($"رقم التلفون: {ExtractWinnerDriverPhone(visit) ?? "777123456"}").FontSize(9).DirectionFromRightToLeft();
                            cell.Item().PaddingVertical(15);
                            cell.Item().LineHorizontal(1).LineColor("#000000");
                            cell.Item().AlignCenter().Text("التوقيع").FontSize(9).DirectionFromRightToLeft();
                        });

                        // القائم بالمهمة
                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("القائم بالمهمة:").FontSize(10).Bold().DirectionFromRightToLeft();
                            cell.Item().PaddingVertical(2);
                            cell.Item().Text("الاسم: علي أحمد العبدي").FontSize(9).DirectionFromRightToLeft();
                            cell.Item().Text("المنصب: سكرتاريه التغذية").FontSize(9).DirectionFromRightToLeft();
                            cell.Item().PaddingVertical(15);
                            cell.Item().LineHorizontal(1).LineColor("#000000");
                            cell.Item().AlignCenter().Text("التوقيع").FontSize(9).DirectionFromRightToLeft();
                        });
                    });
                });

                column.Item().PaddingVertical(1);

                // قسم مدير الفرع
                column.Item().Border(1).BorderColor("#000000").Column(col =>
                {
                    // عنوان القسم
                    col.Item().Background("#E8E8E8").Padding(5).AlignCenter()
                        .Text("اعتماد مدير الفرع").FontSize(11).Bold().DirectionFromRightToLeft();

                    // محتوى الاعتماد
                    col.Item().Padding(8).Column(content =>
                    {
                        content.Item().AlignCenter().Text("م/محمد محمد الديلمي").FontSize(11).DirectionFromRightToLeft();
                        content.Item().PaddingVertical(20);
                        content.Item().LineHorizontal(1).LineColor("#000000");
                        content.Item().AlignCenter().Text("التوقيع والختم").FontSize(9).DirectionFromRightToLeft();
                    });
                });
            });
        }

        /// <summary>
        /// إنشاء تفاصيل الخصم الاحترافية للطباعة
        /// </summary>
        private static void CreateProfessionalDeductionDetails(IContainer container, FieldVisit visit, int deductionDays, DateTime newReturnDate, string reason, decimal dailyRate)
        {
            // استخراج المبلغ الأصلي من بيانات السائق الفائز
            var originalAmount = ExtractWinnerAmount(visit);
            var deductionAmount = deductionDays * dailyRate;
            var finalAmount = originalAmount - deductionAmount;

            container.Column(column =>
            {
                column.Item().PaddingVertical(2);

                // قسم تفاصيل الخصم
                column.Item().Border(1).BorderColor("#000000").Column(col =>
                {
                    // عنوان القسم
                    col.Item().Background("#E8E8E8").Padding(5).AlignCenter()
                        .Text("تفاصيل الخصم").FontSize(11).Bold().DirectionFromRightToLeft();

                    // محتوى التفاصيل
                    col.Item().Padding(8).Table(table =>
                    {
                        table.ColumnsDefinition(columns =>
                        {
                            columns.RelativeColumn(1);
                            columns.RelativeColumn(1);
                        });

                        // الصف الأول: التواريخ
                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("تاريخ العودة الأصلي:").FontSize(9).Bold().DirectionFromRightToLeft();
                            cell.Item().Text(visit.ReturnDate.ToString("dd/MM/yyyy")).FontSize(10).DirectionFromRightToLeft();
                        });

                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("تاريخ العودة الجديد:").FontSize(9).Bold().DirectionFromRightToLeft();
                            cell.Item().Text(newReturnDate.ToString("dd/MM/yyyy")).FontSize(10).DirectionFromRightToLeft();
                        });

                        // الصف الثاني: الأيام والمبلغ الأصلي
                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("عدد الأيام المخصومة:").FontSize(9).Bold().DirectionFromRightToLeft();
                            cell.Item().Text($"{deductionDays} يوم").FontSize(10).DirectionFromRightToLeft();
                        });

                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("المبلغ الأصلي:").FontSize(9).Bold().DirectionFromRightToLeft();
                            cell.Item().Text($"{originalAmount:N0} ريال").FontSize(10).DirectionFromRightToLeft();
                        });

                        // الصف الثالث: مبلغ الخصم والمبلغ النهائي
                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("مبلغ الخصم:").FontSize(9).Bold().DirectionFromRightToLeft();
                            cell.Item().Text($"{deductionAmount:N0} ريال").FontSize(10).DirectionFromRightToLeft();
                        });

                        table.Cell().Padding(5).Column(cell =>
                        {
                            cell.Item().Text("المبلغ النهائي:").FontSize(9).Bold().DirectionFromRightToLeft();
                            cell.Item().Text($"{finalAmount:N0} ريال").FontSize(11).Bold().DirectionFromRightToLeft();
                        });
                    });
                });

                column.Item().PaddingVertical(10);

                // السبب مضغوط
                column.Item().Border(2).BorderColor("#000000").Padding(0).Column(col =>
                {
                    col.Item().Border(1).BorderColor("#000000").Padding(6).AlignRight()
                        .Text("السبب: 📝").FontSize(11).Bold().DirectionFromRightToLeft();

                    col.Item().Border(1).BorderColor("#000000").Padding(8).MinHeight(40).AlignRight()
                        .Text(reason).FontSize(10).DirectionFromRightToLeft();
                });
            });
        }

        /// <summary>
        /// إنشاء تفاصيل الخصم الاحترافية
        /// </summary>
        private static void CreateDeductionDetails(IContainer container, FieldVisit visit, int deductionDays, DateTime newReturnDate, string reason, decimal dailyRate)
        {
            // استخراج المبلغ الأصلي من بيانات السائق الفائز
            var originalAmount = ExtractWinnerAmount(visit);
            var deductionAmount = deductionDays * dailyRate;
            var finalAmount = originalAmount - deductionAmount;

            container.Padding(10).Column(column =>
            {
                // عنوان قسم تفاصيل الخصم
                column.Item().Background("#E74C3C").Padding(10).Row(row =>
                {
                    row.RelativeItem().Text("➖ تفاصيل الخصم").FontSize(14).Bold().FontColor("#FFFFFF");
                });

                // بطاقة تفاصيل السائق والمهمة
                column.Item().Border(1).BorderColor("#BDC3C7").Background("#F8F9FA").Padding(15).Column(col =>
                {
                    // معلومات السائق
                    col.Item().Row(row =>
                    {
                        row.RelativeItem().Column(c =>
                        {
                            c.Item().AlignRight().Text("🔄 تاريخ العودة الجديد").FontSize(12).Bold().FontColor("#2C3E50").DirectionFromRightToLeft();
                            c.Item().AlignRight().Text(newReturnDate.ToString("dd/MM/yyyy")).FontSize(11).FontColor("#34495E").DirectionFromRightToLeft();
                        });

                        row.RelativeItem().Column(c =>
                        {
                            c.Item().AlignRight().Text("📅 تاريخ المغادرة").FontSize(12).Bold().FontColor("#2C3E50").DirectionFromRightToLeft();
                            c.Item().AlignRight().Text(visit.DepartureDate.ToString("dd/MM/yyyy")).FontSize(11).FontColor("#34495E").DirectionFromRightToLeft();
                        });

                        row.RelativeItem().Column(c =>
                        {
                            c.Item().AlignRight().Text("👨‍💼 السائق").FontSize(12).Bold().FontColor("#2C3E50").DirectionFromRightToLeft();
                            c.Item().AlignRight().Text(ExtractWinnerDriverName(visit)).FontSize(11).FontColor("#34495E").DirectionFromRightToLeft();
                        });
                    });

                    col.Item().PaddingVertical(10);

                    // المبالغ المالية
                    col.Item().Background("#ECF0F1").Padding(10).Row(row =>
                    {
                        row.RelativeItem().Column(c =>
                        {
                            c.Item().AlignRight().Text("💎 المبلغ النهائي").FontSize(12).Bold().FontColor("#8E44AD").DirectionFromRightToLeft();
                            c.Item().AlignRight().Text($"{finalAmount:N0} ريال").FontSize(11).FontColor("#8E44AD").DirectionFromRightToLeft();
                        });

                        row.RelativeItem().Column(c =>
                        {
                            c.Item().AlignRight().Text("➖ مبلغ الخصم").FontSize(12).Bold().FontColor("#E74C3C").DirectionFromRightToLeft();
                            c.Item().AlignRight().Text($"{deductionAmount:N0} ريال").FontSize(11).FontColor("#E74C3C").DirectionFromRightToLeft();
                        });

                        row.RelativeItem().Column(c =>
                        {
                            c.Item().AlignRight().Text("💰 المبلغ الأصلي").FontSize(12).Bold().FontColor("#2C3E50").DirectionFromRightToLeft();
                            c.Item().AlignRight().Text($"{originalAmount:N0} ريال").FontSize(11).FontColor("#34495E").DirectionFromRightToLeft();
                        });
                    });
                });

                column.Item().PaddingVertical(10);

                // قسم السبب والملاحظات
                column.Item().Background("#F39C12").Padding(10).Row(row =>
                {
                    row.RelativeItem().Text("📝 السبب والملاحظات").FontSize(14).Bold().FontColor("#FFFFFF");
                });

                column.Item().Border(1).BorderColor("#BDC3C7").Background("#FEF9E7").Padding(15).Column(col =>
                {
                    // سبب الخصم
                    col.Item().Column(c =>
                    {
                        c.Item().Text("📋 سبب الخصم:").FontSize(12).Bold().FontColor("#2C3E50");
                        c.Item().PaddingVertical(5);
                        c.Item().Border(1).BorderColor("#BDC3C7").Background("#FFFFFF").Padding(10).MinHeight(60)
                            .Text(reason).FontSize(11).FontColor("#34495E");
                    });

                    col.Item().PaddingVertical(10);

                    // الملاحظات
                    col.Item().Column(c =>
                    {
                        c.Item().Text("💬 ملاحظات إضافية:").FontSize(12).Bold().FontColor("#2C3E50");
                        c.Item().PaddingVertical(5);
                        c.Item().Border(1).BorderColor("#BDC3C7").Background("#FFFFFF").Padding(10).MinHeight(40)
                            .Text("لا توجد ملاحظات إضافية").FontSize(11).FontColor("#7F8C8D");
                    });
                });

            });
        }

        /// <summary>
        /// إنشاء قسم التوقيعات الاحترافي
        /// </summary>
        private static void CreateSignatures(IContainer container)
        {
            container.Padding(10).Column(column =>
            {
                // عنوان قسم التوقيعات
                column.Item().Background("#8E44AD").Padding(10).Row(row =>
                {
                    row.RelativeItem().Text("✍️ التوقيعات والاعتماد").FontSize(14).Bold().FontColor("#FFFFFF");
                });

                column.Item().PaddingVertical(10);

                // صف التوقيعات
                column.Item().Row(row =>
                {
                    // التوقيع الأول - الموظف المختص
                    row.RelativeItem().Border(1).BorderColor("#BDC3C7").Background("#F8F9FA").Padding(15).Column(col =>
                    {
                        col.Item().Background("#3498DB").Padding(8).AlignCenter()
                            .Text("👨‍💼 القائم بالزيارة").FontSize(12).Bold().FontColor("#FFFFFF");

                        col.Item().PaddingVertical(10);

                        col.Item().AlignCenter().Text("").FontSize(11).Bold().FontColor("#2C3E50");
                        col.Item().AlignCenter().Text("م").FontSize(10).FontColor("#7F8C8D");

                        col.Item().PaddingVertical(20);

                        col.Item().Border(1).BorderColor("#BDC3C7").MinHeight(40).AlignCenter().AlignMiddle()
                            .Text("التوقيع").FontSize(10).FontColor("#95A5A6");

                    });

                    row.ConstantItem(20); // مسافة بين التوقيعات

                    // التوقيع الثاني - مدير الفرع
                    row.RelativeItem().Border(1).BorderColor("#BDC3C7").Background("#F8F9FA").Padding(15).Column(col =>
                    {
                        col.Item().Background("#E74C3C").Padding(8).AlignCenter()
                            .Text("👔 مدير الفرع").FontSize(12).Bold().FontColor("#FFFFFF");

                        col.Item().PaddingVertical(10);

                        col.Item().AlignCenter().Text("م/ محمد محمد الديلمي").FontSize(11).Bold().FontColor("#2C3E50");
                        col.Item().AlignCenter().Text("مدير الفرع").FontSize(10).FontColor("#7F8C8D");

                        col.Item().PaddingVertical(20);

                        col.Item().Border(1).BorderColor("#BDC3C7").MinHeight(40).AlignCenter().AlignMiddle()
                            .Text("التوقيع والاعتماد").FontSize(10).FontColor("#95A5A6");

                        col.Item().PaddingVertical(5);
                    });
                });

                column.Item().PaddingVertical(10);

            });
        }

        /// <summary>
        /// استخراج اسم السائق الفائز من بيانات الزيارة
        /// </summary>
        private static string ExtractWinnerDriverName(FieldVisit visit)
        {
            try
            {
                // البحث في حقل SelectedDrivers عن السائق الفائز
                if (!string.IsNullOrEmpty(visit.SelectedDrivers))
                {
                    var driversData = visit.SelectedDrivers.Split(new char[] { '\n', '|' }, StringSplitOptions.RemoveEmptyEntries);

                    foreach (var driverData in driversData)
                    {
                        var parts = driverData.Split(" - ");
                        if (parts.Length >= 3 && (parts[2].Contains("🏆 فائز") || parts[2].Contains("فائز")))
                        {
                            return parts[0].Trim();
                        }
                    }
                }

                // إذا لم نجد، نستخدم حقل DriverContract
                if (!string.IsNullOrEmpty(visit.DriverContract))
                {
                    return visit.DriverContract;
                }

                return ""; // قيمة افتراضية
            }
            catch
            {
                return ""; // قيمة افتراضية
            }
        }

        /// <summary>
        /// استخراج مبلغ السائق الفائز من بيانات الزيارة
        /// </summary>
        private static decimal ExtractWinnerAmount(FieldVisit visit)
        {
            try
            {
                // البحث في حقل SelectedDrivers عن مبلغ السائق الفائز
                if (!string.IsNullOrEmpty(visit.SelectedDrivers))
                {
                    var driversData = visit.SelectedDrivers.Split(new char[] { '\n', '|' }, StringSplitOptions.RemoveEmptyEntries);

                    foreach (var driverData in driversData)
                    {
                        var parts = driverData.Split(" - ");
                        if (parts.Length >= 3 && (parts[2].Contains("🏆 فائز") || parts[2].Contains("فائز")))
                        {
                            // استخراج المبلغ من الجزء الثاني (مثال: "40,000 ريال")
                            if (parts.Length >= 2)
                            {
                                var amountText = parts[1].Replace(" ريال", "").Replace(",", "").Trim();
                                if (decimal.TryParse(amountText, out decimal amount))
                                {
                                    return amount;
                                }
                            }
                        }
                    }
                }

                return 54000; // قيمة افتراضية
            }
            catch
            {
                return 54000; // قيمة افتراضية
            }
        }

        /// <summary>
        /// حفظ التقرير وفتحه
        /// </summary>
        public static void SaveAndOpenReport(byte[] pdfBytes, string fileName)
        {
            try
            {
                var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                var reportsFolder = Path.Combine(documentsPath, "تقارير النظام");

                if (!Directory.Exists(reportsFolder))
                {
                    Directory.CreateDirectory(reportsFolder);
                }

                var filePath = Path.Combine(reportsFolder, $"{fileName}_{DateTime.Now:yyyyMMdd_HHmmss}.pdf");
                File.WriteAllBytes(filePath, pdfBytes);

                // فتح الملف
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = filePath,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ أو فتح التقرير: {ex.Message}");
            }
        }

        /// <summary>
        /// استخراج رقم تلفون السائق الفائز من بيانات الزيارة
        /// </summary>
        private static string ExtractWinnerDriverPhone(FieldVisit visit)
        {
            try
            {
                // البحث في حقل SelectedDrivers عن السائق الفائز
                if (!string.IsNullOrEmpty(visit.SelectedDrivers))
                {
                    var driversData = visit.SelectedDrivers.Split(new char[] { '\n', '|' }, StringSplitOptions.RemoveEmptyEntries);

                    foreach (var driverData in driversData)
                    {
                        var parts = driverData.Split(" - ");
                        if (parts.Length >= 3 && (parts[2].Contains("🏆 فائز") || parts[2].Contains("فائز")))
                        {
                            // البحث عن رقم التلفون في البيانات
                            if (parts.Length >= 2)
                            {
                                return parts[1].Trim(); // رقم التلفون عادة في الجزء الثاني
                            }
                        }
                    }
                }

                return "777123456"; // رقم افتراضي
            }
            catch
            {
                return "777123456";
            }
        }
    }
}
