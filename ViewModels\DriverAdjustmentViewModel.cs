using System;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using Prism.Commands;
using Prism.Mvvm;
using DriverManagementSystem.Models;
using DriverManagementSystem.Data;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Collections.Generic;

namespace DriverManagementSystem.ViewModels
{
    /// <summary>
    /// ViewModel لإدارة الإضافات والخصومات للسائقين
    /// </summary>
    public class DriverAdjustmentViewModel : BindableBase
    {
        private readonly ApplicationDbContext _context;
        private FieldVisit _selectedVisit;
        private string _winnerDriverName;
        private string _visitConductor;
        private decimal _originalAmount;
        private int _originalDays;
        private DateTime _departureDate;
        private DateTime _returnDate;
        private decimal _dailyRate;

        // خصائص الإضافة
        private int _additionDays;
        private DateTime? _newReturnDateAddition;
        private string _additionReason = string.Empty;

        // خصائص الخصم
        private int _deductionDays;
        private DateTime? _newReturnDateDeduction;
        private string _deductionReason = string.Empty;

        public DriverAdjustmentViewModel(ApplicationDbContext context)
        {
            _context = context;
            InitializeCommands();
        }

        #region Properties

        public string VisitNumber => _selectedVisit?.VisitNumber ?? "";

        public string VisitConductor
        {
            get => _visitConductor;
            set => SetProperty(ref _visitConductor, value);
        }

        public string MissionPurpose => _selectedVisit?.MissionPurpose ?? "";

        public string WinnerDriverName
        {
            get => _winnerDriverName;
            set => SetProperty(ref _winnerDriverName, value);
        }

        public string OriginalAmountText => _originalAmount.ToString("N0") + " ريال";
        public string OriginalDaysText => _originalDays + " يوم";
        public string DailyRateText => _dailyRate.ToString("N0") + " ريال/يوم";
        public string DepartureDateText => _departureDate.ToString("dd/MM/yyyy");
        public string ReturnDateText => _returnDate.ToString("dd/MM/yyyy");

        // خصائص الإضافة
        public int AdditionDays
        {
            get => _additionDays;
            set
            {
                if (SetProperty(ref _additionDays, value))
                {
                    CalculateAddition();
                }
            }
        }

        public DateTime? NewReturnDateAddition
        {
            get => _newReturnDateAddition;
            set
            {
                if (SetProperty(ref _newReturnDateAddition, value))
                {
                    ((DelegateCommand)SaveAdditionCommand).RaiseCanExecuteChanged();
                }
            }
        }

        public string AdditionReason
        {
            get => _additionReason;
            set
            {
                if (SetProperty(ref _additionReason, value))
                {
                    ((DelegateCommand)SaveAdditionCommand).RaiseCanExecuteChanged();
                }
            }
        }

        public string AdditionAmountText => (_dailyRate * _additionDays).ToString("N0") + " ريال";
        public string FinalAmountAdditionText => (_originalAmount + (_dailyRate * _additionDays)).ToString("N0") + " ريال";
        public string FinalDaysAdditionText => (_originalDays + _additionDays) + " يوم";

        // خصائص موحدة للربط
        public string AdjustmentAmountText =>
            _additionDays > 0 ? AdditionAmountText :
            _deductionDays > 0 ? DeductionAmountText : "0 ريال";

        public string FinalAmountText =>
            _additionDays > 0 ? FinalAmountAdditionText :
            _deductionDays > 0 ? FinalAmountDeductionText : _originalAmount.ToString("N0") + " ريال";

        // خصائص الخصم
        public int DeductionDays
        {
            get => _deductionDays;
            set
            {
                if (SetProperty(ref _deductionDays, value))
                {
                    CalculateDeduction();
                }
            }
        }

        public DateTime? NewReturnDateDeduction
        {
            get => _newReturnDateDeduction;
            set
            {
                if (SetProperty(ref _newReturnDateDeduction, value))
                {
                    ((DelegateCommand)SaveDeductionCommand).RaiseCanExecuteChanged();
                }
            }
        }

        public string DeductionReason
        {
            get => _deductionReason;
            set
            {
                if (SetProperty(ref _deductionReason, value))
                {
                    ((DelegateCommand)SaveDeductionCommand).RaiseCanExecuteChanged();
                }
            }
        }

        public string DeductionAmountText => (_dailyRate * _deductionDays).ToString("N0") + " ريال";
        public string FinalAmountDeductionText => Math.Max(0, _originalAmount - (_dailyRate * _deductionDays)).ToString("N0") + " ريال";
        public string FinalDaysDeductionText => Math.Max(0, _originalDays - _deductionDays) + " يوم";

        #endregion

        #region Commands

        public ICommand SaveAdditionCommand { get; private set; }
        public ICommand SaveDeductionCommand { get; private set; }
        public ICommand CancelCommand { get; private set; }

        private void InitializeCommands()
        {
            SaveAdditionCommand = new DelegateCommand(async () => await SaveAdditionAsync(), CanSaveAddition);
            SaveDeductionCommand = new DelegateCommand(async () => await SaveDeductionAsync(), CanSaveDeduction);
            CancelCommand = new DelegateCommand(Cancel);
        }

        private bool CanSaveAddition()
        {
            return _additionDays > 0 && !string.IsNullOrWhiteSpace(_additionReason) && _newReturnDateAddition.HasValue;
        }

        private bool CanSaveDeduction()
        {
            return _deductionDays > 0 && _deductionDays < _originalDays && 
                   !string.IsNullOrWhiteSpace(_deductionReason) && _newReturnDateDeduction.HasValue;
        }

        #endregion

        #region Methods

        /// <summary>
        /// تحميل بيانات الزيارة
        /// </summary>
        public async Task LoadVisitDataAsync(FieldVisit visit)
        {
            try
            {
                _selectedVisit = visit;
                _departureDate = visit.DepartureDate;
                _returnDate = visit.ReturnDate;
                _originalDays = visit.DaysCount;

                // جلب بيانات القائمين بالزيارة
                await LoadVisitConductorDataAsync();

                // البحث عن السائق الفائز والمبلغ
                await LoadWinnerDriverDataAsync();

                // تحميل البيانات المحفوظة مسبقاً إن وجدت
                await LoadExistingAdjustmentsAsync();

                // حساب الأجر اليومي
                if (_originalDays > 0 && _originalAmount > 0)
                {
                    _dailyRate = _originalAmount / _originalDays;
                }

                // تحديث الواجهة
                RaisePropertyChanged(nameof(VisitNumber));
                RaisePropertyChanged(nameof(VisitConductor));
                RaisePropertyChanged(nameof(MissionPurpose));
                RaisePropertyChanged(nameof(WinnerDriverName));
                RaisePropertyChanged(nameof(OriginalAmountText));
                RaisePropertyChanged(nameof(OriginalDaysText));
                RaisePropertyChanged(nameof(DepartureDateText));
                RaisePropertyChanged(nameof(ReturnDateText));
                RaisePropertyChanged(nameof(DailyRateText));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الزيارة: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل بيانات القائمين بالزيارة
        /// </summary>
        private async Task LoadVisitConductorDataAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔍 البحث عن القائمين بالزيارة للزيارة ID: {_selectedVisit.Id}");

                // جلب بيانات القائمين بالزيارة من قاعدة البيانات
                var visitors = await _context.FieldVisitors
                    .Where(v => v.FieldVisitId == _selectedVisit.Id)
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"🔍 تم العثور على {visitors?.Count ?? 0} قائم بالزيارة");

                if (visitors?.Any() == true)
                {
                    // تجميع أسماء القائمين بالزيارة
                    var visitorNames = visitors.Select(v => $"{v.OfficerRank} {v.OfficerName}").ToList();
                    VisitConductor = string.Join(" - ", visitorNames);
                    System.Diagnostics.Debug.WriteLine($"✅ القائمين بالزيارة: {VisitConductor}");
                }
                else
                {
                    VisitConductor = "لم يتم تحديد القائم بالزيارة";
                    System.Diagnostics.Debug.WriteLine($"⚠️ لم يتم العثور على قائمين بالزيارة");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل بيانات القائم بالزيارة: {ex.Message}");
                VisitConductor = "خطأ في تحميل البيانات";
            }
        }

        /// <summary>
        /// تحميل بيانات السائق الفائز
        /// </summary>
        private async Task LoadWinnerDriverDataAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔍 البحث عن السائق الفائز للزيارة: {_selectedVisit.VisitNumber}");

                // البحث في جدول العروض عن السائق الفائز
                var winnerQuote = await _context.DriverQuotes
                    .Where(dq => dq.VisitNumber == _selectedVisit.VisitNumber &&
                                dq.Status == QuoteStatus.Accepted)
                    .FirstOrDefaultAsync();

                if (winnerQuote != null)
                {
                    WinnerDriverName = winnerQuote.DriverName;
                    _originalAmount = winnerQuote.QuotedPrice;
                    System.Diagnostics.Debug.WriteLine($"✅ تم العثور على السائق الفائز في جدول العروض: {WinnerDriverName} - {_originalAmount} ريال");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"🔍 لم يتم العثور على السائق في جدول العروض، البحث في SelectedDrivers: {_selectedVisit.SelectedDrivers}");

                    // البحث في بيانات الزيارة المحفوظة (SelectedDrivers)
                    if (!string.IsNullOrEmpty(_selectedVisit.SelectedDrivers))
                    {
                        var driversData = _selectedVisit.SelectedDrivers.Split(" | ");
                        System.Diagnostics.Debug.WriteLine($"🔍 تم تقسيم البيانات إلى {driversData.Length} جزء");

                        foreach (var driverData in driversData)
                        {
                            System.Diagnostics.Debug.WriteLine($"🔍 فحص البيانات: {driverData}");
                            var parts = driverData.Split(" - ");
                            if (parts.Length >= 3 && (parts[2].Contains("🏆 فائز") || parts[2].Contains("فائز")))
                            {
                                WinnerDriverName = parts[0];
                                // استخراج المبلغ من الجزء الثاني (مثال: "40,000 ريال")
                                if (parts.Length >= 2)
                                {
                                    var amountText = parts[1].Replace(" ريال", "").Replace(",", "").Trim();
                                    if (decimal.TryParse(amountText, out decimal amount))
                                    {
                                        _originalAmount = amount;
                                    }
                                }
                                System.Diagnostics.Debug.WriteLine($"✅ تم العثور على السائق الفائز: {WinnerDriverName} - {_originalAmount} ريال");
                                break;
                            }
                        }
                    }
                }

                // إذا لم نجد بيانات، نضع قيم افتراضية
                if (string.IsNullOrEmpty(WinnerDriverName))
                {
                    WinnerDriverName = "لم يتم تحديد السائق الفائز";
                    _originalAmount = 0;
                    System.Diagnostics.Debug.WriteLine($"⚠️ لم يتم العثور على سائق فائز");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل بيانات السائق الفائز: {ex.Message}");
                WinnerDriverName = "خطأ في تحميل البيانات";
                _originalAmount = 0;
            }
        }

        /// <summary>
        /// حساب الإضافة
        /// </summary>
        private void CalculateAddition()
        {
            if (_additionDays > 0)
            {
                NewReturnDateAddition = _returnDate.AddDays(_additionDays);
            }
            else
            {
                NewReturnDateAddition = _returnDate;
            }

            RaisePropertyChanged(nameof(AdditionAmountText));
            RaisePropertyChanged(nameof(FinalAmountAdditionText));
            RaisePropertyChanged(nameof(AdjustmentAmountText));
            RaisePropertyChanged(nameof(FinalAmountText));
            ((DelegateCommand)SaveAdditionCommand).RaiseCanExecuteChanged();
        }

        /// <summary>
        /// حساب الخصم
        /// </summary>
        private void CalculateDeduction()
        {
            if (_deductionDays > 0 && _deductionDays < _originalDays)
            {
                var finalDays = _originalDays - _deductionDays;
                NewReturnDateDeduction = _departureDate.AddDays(finalDays - 1);
            }
            else
            {
                NewReturnDateDeduction = _returnDate;
            }

            RaisePropertyChanged(nameof(DeductionAmountText));
            RaisePropertyChanged(nameof(FinalAmountDeductionText));
            RaisePropertyChanged(nameof(AdjustmentAmountText));
            RaisePropertyChanged(nameof(FinalAmountText));
            ((DelegateCommand)SaveDeductionCommand).RaiseCanExecuteChanged();
        }

        /// <summary>
        /// حفظ الإضافة
        /// </summary>
        private async Task SaveAdditionAsync()
        {
            MessageBox.Show("🔍 بدء عملية حفظ الإضافة - تشخيص مبدئي", "تشخيص", MessageBoxButton.OK, MessageBoxImage.Information);

            try
            {
                // تشخيص البيانات قبل الحفظ
                System.Diagnostics.Debug.WriteLine("🔍 بدء عملية حفظ الإضافة...");
                System.Diagnostics.Debug.WriteLine($"📊 بيانات الزيارة: ID={_selectedVisit?.Id}, Number={_selectedVisit?.VisitNumber}");
                System.Diagnostics.Debug.WriteLine($"👤 اسم السائق: {_winnerDriverName}");
                System.Diagnostics.Debug.WriteLine($"📅 الأيام: الأصلية={_originalDays}, الإضافة={_additionDays}, النهائية={_originalDays + _additionDays}");
                System.Diagnostics.Debug.WriteLine($"💰 المبالغ: الأصلي={_originalAmount}, اليومي={_dailyRate}, الإضافة={_dailyRate * _additionDays}");
                System.Diagnostics.Debug.WriteLine($"📝 السبب: {_additionReason}");

                var diagnosticInfo = $"📊 تشخيص البيانات:\n";
                diagnosticInfo += $"🔸 الزيارة: ID={_selectedVisit?.Id}, Number={_selectedVisit?.VisitNumber}\n";
                diagnosticInfo += $"🔸 السائق: {_winnerDriverName}\n";
                diagnosticInfo += $"🔸 الأيام: أصلية={_originalDays}, إضافة={_additionDays}\n";
                diagnosticInfo += $"🔸 المبالغ: أصلي={_originalAmount}, يومي={_dailyRate}\n";
                diagnosticInfo += $"🔸 السبب: {_additionReason}";

                MessageBox.Show(diagnosticInfo, "تشخيص البيانات", MessageBoxButton.OK, MessageBoxImage.Information);

                // حذف الجدول وإعادة إنشاؤه إذا كان موجوداً
                try
                {
                    await _context.Database.ExecuteSqlRawAsync(@"
                        IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'DriverAdjustments')
                        BEGIN
                            DROP TABLE [dbo].[DriverAdjustments];
                            PRINT 'تم حذف جدول DriverAdjustments';
                        END
                    ");

                    // إنشاء الجدول الجديد
                    await _context.Database.ExecuteSqlRawAsync(@"
                        CREATE TABLE [dbo].[DriverAdjustments] (
                            [Id] int IDENTITY(1,1) NOT NULL PRIMARY KEY,
                            [VisitId] int NOT NULL,
                            [VisitNumber] nvarchar(50) NOT NULL,
                            [DriverName] nvarchar(100) NOT NULL,
                            [DriverCode] nvarchar(20) NULL,
                            [AdjustmentType] nvarchar(20) NOT NULL,
                            [OriginalDays] int NOT NULL,
                            [AdjustmentDays] int NOT NULL,
                            [FinalDays] int NOT NULL,
                            [OriginalDepartureDate] datetime2 NOT NULL,
                            [OriginalReturnDate] datetime2 NOT NULL,
                            [NewReturnDate] datetime2 NOT NULL,
                            [OriginalAmount] decimal(18,2) NOT NULL,
                            [DailyRate] decimal(18,2) NOT NULL,
                            [AdjustmentAmount] decimal(18,2) NOT NULL,
                            [FinalAmount] decimal(18,2) NOT NULL,
                            [Reason] nvarchar(500) NOT NULL,
                            [CreatedDate] datetime2 NOT NULL DEFAULT GETDATE(),
                            [CreatedBy] nvarchar(100) NOT NULL DEFAULT 'النظام',
                            [Notes] nvarchar(1000) NULL,
                            [IsActive] bit NOT NULL DEFAULT 1
                        );
                    ");

                    System.Diagnostics.Debug.WriteLine("✅ تم إنشاء جدول DriverAdjustments بنجاح");
                }
                catch (Exception dbEx)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ خطأ في إنشاء الجدول: {dbEx.Message}");
                }

                // التحقق من صحة البيانات
                if (_selectedVisit == null)
                {
                    throw new InvalidOperationException("الزيارة المحددة غير صحيحة");
                }

                if (string.IsNullOrEmpty(_winnerDriverName))
                {
                    throw new InvalidOperationException("اسم السائق مطلوب");
                }

                if (_additionDays <= 0)
                {
                    throw new InvalidOperationException("عدد أيام الإضافة يجب أن يكون أكبر من صفر");
                }

                if (string.IsNullOrEmpty(_additionReason))
                {
                    throw new InvalidOperationException("سبب الإضافة مطلوب");
                }

                if (!_newReturnDateAddition.HasValue)
                {
                    throw new InvalidOperationException("تاريخ العودة الجديد مطلوب");
                }

                // التحقق من وجود الجدول في قاعدة البيانات
                System.Diagnostics.Debug.WriteLine("🔍 التحقق من وجود جدول DriverAdjustments...");
                var tableExists = await _context.Database.ExecuteSqlRawAsync(@"
                    SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
                    WHERE TABLE_NAME = 'DriverAdjustments'");
                System.Diagnostics.Debug.WriteLine($"📋 نتيجة فحص الجدول: {tableExists}");

                var adjustment = new DriverAdjustment
                {
                    VisitId = _selectedVisit.Id,
                    VisitNumber = _selectedVisit.VisitNumber,
                    DriverName = _winnerDriverName,
                    AdjustmentType = "إضافة",
                    OriginalDays = _originalDays,
                    AdjustmentDays = _additionDays,
                    FinalDays = _originalDays + _additionDays,
                    OriginalDepartureDate = _departureDate,
                    OriginalReturnDate = _returnDate,
                    NewReturnDate = _newReturnDateAddition.Value,
                    OriginalAmount = _originalAmount,
                    DailyRate = _dailyRate,
                    AdjustmentAmount = _dailyRate * _additionDays,
                    FinalAmount = _originalAmount + (_dailyRate * _additionDays),
                    Reason = _additionReason,
                    CreatedDate = DateTime.Now,
                    CreatedBy = "النظام",
                    IsActive = true
                };

                System.Diagnostics.Debug.WriteLine("➕ إضافة السجل إلى Context...");
                _context.DriverAdjustments.Add(adjustment);

                System.Diagnostics.Debug.WriteLine("💾 حفظ التغييرات...");
                await _context.SaveChangesAsync();

                System.Diagnostics.Debug.WriteLine("✅ تم حفظ الإضافة بنجاح!");
                MessageBox.Show("تم حفظ الإضافة بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                // إغلاق النافذة
                Application.Current.Windows.OfType<Views.DriverAdjustmentWindow>().FirstOrDefault()?.Close();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ مفصل في حفظ الإضافة:");
                System.Diagnostics.Debug.WriteLine($"   النوع: {ex.GetType().Name}");
                System.Diagnostics.Debug.WriteLine($"   الرسالة: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"   المصدر: {ex.Source}");
                System.Diagnostics.Debug.WriteLine($"   Stack Trace: {ex.StackTrace}");

                var errorDetails = $"❌ خطأ في حفظ الإضافة:\n\n";
                errorDetails += $"🔸 نوع الخطأ: {ex.GetType().Name}\n";
                errorDetails += $"🔸 الرسالة: {ex.Message}\n";

                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"   InnerException: {ex.InnerException.GetType().Name}");
                    System.Diagnostics.Debug.WriteLine($"   InnerMessage: {ex.InnerException.Message}");
                    errorDetails += $"\n🔸 تفاصيل إضافية:\n";
                    errorDetails += $"   النوع: {ex.InnerException.GetType().Name}\n";
                    errorDetails += $"   الرسالة: {ex.InnerException.Message}\n";
                }

                if (ex.Data.Count > 0)
                {
                    errorDetails += $"\n🔸 بيانات إضافية:\n";
                    foreach (var key in ex.Data.Keys)
                    {
                        errorDetails += $"   {key}: {ex.Data[key]}\n";
                    }
                }

                MessageBox.Show(errorDetails, "خطأ مفصل في حفظ الإضافة", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حفظ الخصم
        /// </summary>
        private async Task SaveDeductionAsync()
        {
            try
            {
                var adjustment = new DriverAdjustment
                {
                    VisitId = _selectedVisit.Id,
                    VisitNumber = _selectedVisit.VisitNumber,
                    DriverName = _winnerDriverName,
                    AdjustmentType = "خصم",
                    OriginalDays = _originalDays,
                    AdjustmentDays = _deductionDays,
                    FinalDays = _originalDays - _deductionDays,
                    OriginalDepartureDate = _departureDate,
                    OriginalReturnDate = _returnDate,
                    NewReturnDate = _newReturnDateDeduction.Value,
                    OriginalAmount = _originalAmount,
                    DailyRate = _dailyRate,
                    AdjustmentAmount = _dailyRate * _deductionDays,
                    FinalAmount = Math.Max(0, _originalAmount - (_dailyRate * _deductionDays)),
                    Reason = _deductionReason,
                    CreatedDate = DateTime.Now,
                    CreatedBy = "النظام", // يمكن تحديث هذا ليكون المستخدم الحالي
                    IsActive = true
                };

                _context.DriverAdjustments.Add(adjustment);
                await _context.SaveChangesAsync();

                MessageBox.Show("تم حفظ الخصم بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                
                // إغلاق النافذة
                Application.Current.Windows.OfType<Views.DriverAdjustmentWindow>().FirstOrDefault()?.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الخصم: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void Cancel()
        {
            Application.Current.Windows.OfType<Views.DriverAdjustmentWindow>().FirstOrDefault()?.Close();
        }

        /// <summary>
        /// تحميل البيانات المحفوظة مسبقاً للزيارة
        /// </summary>
        private async Task LoadExistingAdjustmentsAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔍 البحث عن التعديلات المحفوظة للزيارة ID: {_selectedVisit.Id}");

                var existingAdjustments = await _context.DriverAdjustments
                    .Where(a => a.VisitId == _selectedVisit.Id && a.IsActive)
                    .OrderByDescending(a => a.CreatedDate)
                    .ToListAsync();

                if (existingAdjustments.Any())
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم العثور على {existingAdjustments.Count} تعديل محفوظ");

                    // تحميل آخر إضافة
                    var lastAddition = existingAdjustments.FirstOrDefault(a => a.AdjustmentType == "إضافة");
                    if (lastAddition != null)
                    {
                        _additionDays = lastAddition.AdjustmentDays;
                        _newReturnDateAddition = lastAddition.NewReturnDate;
                        _additionReason = lastAddition.Reason ?? "";

                        RaisePropertyChanged(nameof(AdditionDays));
                        RaisePropertyChanged(nameof(NewReturnDateAddition));
                        RaisePropertyChanged(nameof(AdditionReason));
                        RaisePropertyChanged(nameof(AdditionAmountText));
                        RaisePropertyChanged(nameof(FinalAmountAdditionText));
                        RaisePropertyChanged(nameof(FinalDaysAdditionText));

                        System.Diagnostics.Debug.WriteLine($"📝 تم تحميل الإضافة: {lastAddition.AdjustmentDays} أيام، السبب: {lastAddition.Reason}");
                    }

                    // تحميل آخر خصم
                    var lastDeduction = existingAdjustments.FirstOrDefault(a => a.AdjustmentType == "خصم");
                    if (lastDeduction != null)
                    {
                        _deductionDays = lastDeduction.AdjustmentDays;
                        _newReturnDateDeduction = lastDeduction.NewReturnDate;
                        _deductionReason = lastDeduction.Reason ?? "";

                        RaisePropertyChanged(nameof(DeductionDays));
                        RaisePropertyChanged(nameof(NewReturnDateDeduction));
                        RaisePropertyChanged(nameof(DeductionReason));
                        RaisePropertyChanged(nameof(DeductionAmountText));
                        RaisePropertyChanged(nameof(FinalAmountDeductionText));
                        RaisePropertyChanged(nameof(FinalDaysDeductionText));

                        System.Diagnostics.Debug.WriteLine($"📝 تم تحميل الخصم: {lastDeduction.AdjustmentDays} أيام، السبب: {lastDeduction.Reason}");
                    }

                    // تحديث حالة الأزرار
                    ((DelegateCommand)SaveAdditionCommand).RaiseCanExecuteChanged();
                    ((DelegateCommand)SaveDeductionCommand).RaiseCanExecuteChanged();
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ لا توجد تعديلات محفوظة لهذه الزيارة");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل التعديلات المحفوظة: {ex.Message}");
            }
        }

        #endregion
    }
}
