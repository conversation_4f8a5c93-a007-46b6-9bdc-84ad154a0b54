<Window x:Class="DriverManagementSystem.Views.DriverAdjustmentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="إدارة الإضافات والخصومات للسائقين"
        Height="700" Width="900"
        WindowState="Maximized"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14"
        Background="#F5F5F5">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Styles/PrintStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

        <!-- أنماط مخصصة -->
        <Style x:Key="ModernTabControl" TargetType="TabControl">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="FontSize" Value="16"/>
        </Style>

        <Style x:Key="ModernTabItem" TargetType="TabItem">
            <Setter Property="Background" Value="#F8F9FA"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1,1,1,0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="15"/>
            <Setter Property="Margin" Value="3,0"/>
            <Style.Triggers>
                <Trigger Property="IsSelected" Value="True">
                    <Setter Property="Background" Value="White"/>
                    <Setter Property="BorderBrush" Value="#2196F3"/>
                    <Setter Property="Foreground" Value="#2196F3"/>
                </Trigger>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#E3F2FD"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Style.Triggers>
                <Trigger Property="IsFocused" Value="True">
                    <Setter Property="BorderBrush" Value="#2196F3"/>
                    <Setter Property="BorderThickness" Value="2"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="ModernDatePicker" TargetType="DatePicker">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="40"/>
            <Style.Triggers>
                <Trigger Property="IsFocused" Value="True">
                    <Setter Property="BorderBrush" Value="#2196F3"/>
                    <Setter Property="BorderThickness" Value="2"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1976D2"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#1565C0"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#BDBDBD"/>
                    <Setter Property="Cursor" Value="Arrow"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="CancelButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#757575"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#616161"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="PrintButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#28A745"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#218838"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="AdditionButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#4CAF50"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#388E3C"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="DeductionButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#F44336"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#D32F2F"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="InfoLabel" TargetType="Label">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#424242"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <Style x:Key="ValueLabel" TargetType="Label">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="15"/>
            <Setter Property="Foreground" Value="#2196F3"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
        </ResourceDictionary>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- عنوان النافذة -->
        <Border Grid.Row="0" Background="#2196F3" CornerRadius="5" Padding="20,15" Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="⚖️" FontSize="24" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock Text="خصم / اضافة للزيارة الميدانية" 
                          FontSize="20" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- معلومات الزيارة -->
        <Border Grid.Row="1" Background="White" BorderBrush="#E0E0E0" BorderThickness="1"
                CornerRadius="8" Padding="20" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <StackPanel Grid.Column="0" Grid.Row="0" Orientation="Horizontal" Margin="0,0,20,10">
                    <Label Content="رقم الزيارة:" Style="{StaticResource InfoLabel}"/>
                    <Label Content="{Binding VisitNumber}" Style="{StaticResource ValueLabel}"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Grid.Row="0" Orientation="Horizontal" Margin="0,0,20,10">
                    <Label Content="القائم بالزيارة:" Style="{StaticResource InfoLabel}"/>
                    <Label Content="{Binding VisitConductor}" Style="{StaticResource ValueLabel}"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                    <Label Content="السائق الفائز:" Style="{StaticResource InfoLabel}"/>
                    <Label Content="{Binding WinnerDriverName}" Style="{StaticResource ValueLabel}"/>
                </StackPanel>

                <!-- الصف الثاني: النشاط -->
                <StackPanel Grid.Column="0" Grid.Row="1" Grid.ColumnSpan="3" Orientation="Horizontal" Margin="0,0,0,10">
                    <Label Content="النشاط:" Style="{StaticResource InfoLabel}"/>
                    <TextBlock Text="{Binding MissionPurpose}" FontSize="14" FontWeight="Normal" Foreground="#333333" TextWrapping="Wrap" VerticalAlignment="Center" Margin="5,0,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="0" Grid.Row="2" Orientation="Horizontal" Margin="0,0,20,10">
                    <Label Content="المبلغ المقدم:" Style="{StaticResource InfoLabel}"/>
                    <Label Content="{Binding OriginalAmountText}" Style="{StaticResource ValueLabel}"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Grid.Row="2" Orientation="Horizontal" Margin="0,0,20,10">
                    <Label Content="عدد الأيام:" Style="{StaticResource InfoLabel}"/>
                    <Label Content="{Binding OriginalDaysText}" Style="{StaticResource ValueLabel}"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Grid.Row="2" Orientation="Horizontal" Margin="0,0,0,10">
                    <Label Content="الأجر اليومي:" Style="{StaticResource InfoLabel}"/>
                    <Label Content="{Binding DailyRateText}" Style="{StaticResource ValueLabel}"/>
                </StackPanel>

                <StackPanel Grid.Column="0" Grid.Row="3" Orientation="Horizontal" Margin="0,0,20,0">
                    <Label Content="تاريخ النزول:" Style="{StaticResource InfoLabel}"/>
                    <Label Content="{Binding DepartureDateText}" Style="{StaticResource ValueLabel}"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Grid.Row="3" Orientation="Horizontal" Margin="0,0,20,0">
                    <Label Content="تاريخ العودة:" Style="{StaticResource InfoLabel}"/>
                    <Label Content="{Binding ReturnDateText}" Style="{StaticResource ValueLabel}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- التبويبات الرئيسية -->
        <TabControl Grid.Row="2" Style="{StaticResource ModernTabControl}">
            <!-- تبويب الإضافة -->
            <TabItem Header="➕ إضافة أيام" Style="{StaticResource ModernTabItem}">
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
                    <StackPanel>
                        <!-- حقول الإضافة -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- عدد الأيام المضافة -->
                            <Label Grid.Column="0" Grid.Row="0" Content="عدد الأيام المضافة:" Style="{StaticResource InfoLabel}" Margin="0,0,0,5"/>
                            <TextBox Grid.Column="0" Grid.Row="1" Text="{Binding AdditionDays, UpdateSourceTrigger=PropertyChanged}" 
                                    Style="{StaticResource ModernTextBox}" Margin="0,0,10,15"/>

                            <!-- تاريخ العودة الجديد -->
                            <Label Grid.Column="1" Grid.Row="0" Content="تاريخ العودة الجديد:" Style="{StaticResource InfoLabel}" Margin="0,0,0,5"/>
                            <DatePicker Grid.Column="1" Grid.Row="1" SelectedDate="{Binding NewReturnDateAddition}" 
                                       Style="{StaticResource ModernDatePicker}" Margin="10,0,0,15"/>

                            <!-- المبلغ المضاف -->
                            <Label Grid.Column="0" Grid.Row="2" Content="المبلغ المضاف:" Style="{StaticResource InfoLabel}" Margin="0,0,0,5"/>
                            <TextBox Grid.Column="0" Grid.Row="3" Text="{Binding AdditionAmountText, Mode=OneWay}" IsReadOnly="True"
                                    Style="{StaticResource ModernTextBox}" Background="#F5F5F5" Margin="0,0,10,15"/>

                            <!-- المبلغ النهائي -->
                            <Label Grid.Column="1" Grid.Row="2" Content="المبلغ المستحق للسائق:" Style="{StaticResource InfoLabel}" Margin="0,0,0,5"/>
                            <TextBox Grid.Column="1" Grid.Row="3" Text="{Binding FinalAmountAdditionText, Mode=OneWay}" IsReadOnly="True"
                                    Style="{StaticResource ModernTextBox}" Background="#E8F5E8" Margin="10,0,0,15"/>

                            <!-- سبب الإضافة -->
                            <Label Grid.Column="0" Grid.Row="4" Grid.ColumnSpan="2" Content="سبب الإضافة:" Style="{StaticResource InfoLabel}" Margin="0,0,0,5"/>
                            <TextBox Grid.Column="0" Grid.Row="5" Grid.ColumnSpan="2" Text="{Binding AdditionReason}" 
                                    Style="{StaticResource ModernTextBox}" Height="80" TextWrapping="Wrap" 
                                    VerticalContentAlignment="Top" AcceptsReturn="True"/>
                        </Grid>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- تبويب الخصم -->
            <TabItem Header="➖ خصم أيام" Style="{StaticResource ModernTabItem}">
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
                    <StackPanel>
                        <!-- حقول الخصم -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- عدد الأيام المخصومة -->
                            <Label Grid.Column="0" Grid.Row="0" Content="عدد الأيام المخصومة:" Style="{StaticResource InfoLabel}" Margin="0,0,0,5"/>
                            <TextBox Grid.Column="0" Grid.Row="1" Text="{Binding DeductionDays, UpdateSourceTrigger=PropertyChanged}" 
                                    Style="{StaticResource ModernTextBox}" Margin="0,0,10,15"/>

                            <!-- تاريخ العودة الجديد -->
                            <Label Grid.Column="1" Grid.Row="0" Content="تاريخ العودة الجديد:" Style="{StaticResource InfoLabel}" Margin="0,0,0,5"/>
                            <DatePicker Grid.Column="1" Grid.Row="1" SelectedDate="{Binding NewReturnDateDeduction}" 
                                       Style="{StaticResource ModernDatePicker}" Margin="10,0,0,15"/>

                            <!-- المبلغ المخصوم -->
                            <Label Grid.Column="0" Grid.Row="2" Content="المبلغ المخصوم:" Style="{StaticResource InfoLabel}" Margin="0,0,0,5"/>
                            <TextBox Grid.Column="0" Grid.Row="3" Text="{Binding DeductionAmountText, Mode=OneWay}" IsReadOnly="True"
                                    Style="{StaticResource ModernTextBox}" Background="#F5F5F5" Margin="0,0,10,15"/>

                            <!-- المبلغ النهائي -->
                            <Label Grid.Column="1" Grid.Row="2" Content="المبلغ المستحق للسائق:" Style="{StaticResource InfoLabel}" Margin="0,0,0,5"/>
                            <TextBox Grid.Column="1" Grid.Row="3" Text="{Binding FinalAmountDeductionText, Mode=OneWay}" IsReadOnly="True"
                                    Style="{StaticResource ModernTextBox}" Background="#FFE8E8" Margin="10,0,0,15"/>

                            <!-- سبب الخصم -->
                            <Label Grid.Column="0" Grid.Row="4" Grid.ColumnSpan="2" Content="سبب الخصم:" Style="{StaticResource InfoLabel}" Margin="0,0,0,5"/>
                            <TextBox Grid.Column="0" Grid.Row="5" Grid.ColumnSpan="2" Text="{Binding DeductionReason}" 
                                    Style="{StaticResource ModernTextBox}" Height="80" TextWrapping="Wrap" 
                                    VerticalContentAlignment="Top" AcceptsReturn="True"/>
                        </Grid>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>

        <!-- أزرار العمليات -->
        <Border Grid.Row="3" Background="White" BorderBrush="#E0E0E0" BorderThickness="1" 
                CornerRadius="5" Padding="20" Margin="0,20,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="💾 حفظ الإضافة" Style="{StaticResource AdditionButton}"
                        Command="{Binding SaveAdditionCommand}" Width="150" Margin="0,0,15,0"/>
                <Button Content="💾 حفظ الخصم" Style="{StaticResource DeductionButton}"
                        Command="{Binding SaveDeductionCommand}" Width="150" Margin="0,0,15,0"/>
                <Button Content="🖨️ طباعة" Style="{StaticResource PrintButton}"
                        Click="PrintButton_Click" Width="120" Margin="0,0,15,0"/>
                <Button Content="❌ إلغاء" Style="{StaticResource CancelButton}"
                        Command="{Binding CancelCommand}" Width="120"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
