using System;
using System.Windows;
using DriverManagementSystem.ViewModels;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// نافذة إدارة الإضافات والخصومات للسائقين
    /// </summary>
    public partial class DriverAdjustmentWindow : Window
    {
        public DriverAdjustmentWindow()
        {
            InitializeComponent();
        }

        public DriverAdjustmentWindow(DriverAdjustmentViewModel viewModel) : this()
        {
            DataContext = viewModel;
        }

        /// <summary>
        /// طباعة تقرير الخصم والإضافة
        /// </summary>
        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is DriverAdjustmentViewModel viewModel)
                {
                    // استخدام خدمة الطباعة المتقدمة الموجودة
                    Services.AdvancedPrintService.ShowPrintDialog(this, "تقرير الخصم والإضافة", this);
                }
                else
                {
                    MessageBox.Show("لا توجد بيانات للطباعة", "تنبيه",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
