<UserControl x:Class="DriverManagementSystem.Views.ReportView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:DriverManagementSystem.Views"
             xmlns:converters="clr-namespace:DriverManagementSystem.Views"
             FlowDirection="RightToLeft"
             Background="White">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Styles/PrintStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- Converter للتحقق من وجود الصور -->
            <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>

            <!-- Converter مخصص للتحقق من وجود النص/الصورة -->
            <local:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>

            <!-- Converter للتحقق من النص الفارغ -->
            <local:EmptyStringToVisibilityConverter x:Key="EmptyStringToVisibilityConverter"/>



        </ResourceDictionary>
    </UserControl.Resources>

    <!-- Container الرئيسي -->
    <Grid>
        <!-- A4 Report Container - All Pages in One View -->
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header with Edit Contract Button -->
            <Border Grid.Row="0" Background="#F8F9FA" Padding="15,10" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="📄 تقرير الزيارة الميدانية"
                             FontSize="16" FontWeight="Bold" Foreground="#495057" VerticalAlignment="Center"/>

                        <!-- رقم الزيارة -->
                        <Border Background="#F0FFF0" BorderBrush="#228B22" BorderThickness="1" Padding="8,4" Margin="20,0,0,0">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <TextBlock Text="🔢" FontSize="12" Margin="0,0,5,0" VerticalAlignment="Center"/>
                                <TextBlock FontSize="12" FontWeight="Bold" Foreground="#333333" VerticalAlignment="Center">
                                <Run Text="رقم الزيارة: "/>
                                <Run Text="{Binding ReportData.VisitNumber, FallbackValue=---}"/>
                                </TextBlock>
                            </StackPanel>
                        </Border>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right">

                        <Button Content="🖨️ طباعة العقد"
                            Click="PrintReportButton_Click"
                            Background="#28A745"
                            Foreground="White"
                            Padding="20,10"
                            FontWeight="Bold"
                            FontSize="14"
                            BorderThickness="0"
                            Margin="0,0,15,0"
                            MinWidth="140">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Cursor" Value="Hand"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#218838"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                        </Button>


                    </StackPanel>
                </Grid>
            </Border>

            <!-- Report Content -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto" Background="White">
                <StackPanel Background="White" HorizontalAlignment="Center">

                    <!-- الصفحة الأولى: استمارة طلب نزول ميداني -->
                    <Border Style="{StaticResource PrintPageStyle}" Width="730">
                        <StackPanel Margin="20" TextElement.FontFamily="Arial">

                            <!-- رأس الصفحة -->
                            <Grid Margin="0,0,0,20">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="150"/>
                                </Grid.ColumnDefinitions>

                                <!-- الشعار -->
                                <StackPanel Grid.Column="0" HorizontalAlignment="Left" VerticalAlignment="Top">
                                    <Image Source="C:\Users\<USER>\Desktop\sys\icons\sfd.png"
                                       Width="100" Height="57" Stretch="Uniform"/>
                                </StackPanel>

                                <!-- العنوان الرئيسي -->
                                <Border Grid.Column="1" BorderBrush="Black" BorderThickness="2"
                                    Padding="15,10" HorizontalAlignment="Center" VerticalAlignment="Top" Width="284">
                                    <TextBlock Text="استمارة طلب نزول ميداني"
                                           FontSize="18" FontWeight="Bold"
                                           HorizontalAlignment="Center" VerticalAlignment="Center"
                                           Foreground="Black" TextAlignment="Center"/>
                                </Border>

                                <!-- التاريخ ورقم الزيارة -->
                                <StackPanel Grid.Column="2" HorizontalAlignment="Right" VerticalAlignment="Top">
                                    <Border Background="#F0F8FF" BorderBrush="#4682B4" BorderThickness="1" Padding="8,4" Height="35">
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <TextBlock Text="📅" FontSize="12" Margin="0,0,5,0" VerticalAlignment="Center"/>
                                            <TextBlock FontSize="10" FontWeight="Bold" Foreground="#333333" VerticalAlignment="Center"><Run Text="التاريخ: "/><Run Text=" "/><Run Text="{Binding ReportData.ReportDate, FallbackValue=24/06/2025}"/></TextBlock>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </Grid>

                            <!-- خط فاصل -->
                            <Border Height="2" Background="#333333" Margin="0,0,0,15"/>

                            <!-- محتوى الاستمارة -->
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- القسم المدمج: معلومات الزيارة الأساسية -->
                                <Border Grid.Row="0" BorderBrush="#1E40AF" BorderThickness="2" Margin="0,0,0,15" Background="#EFF6FF" Padding="15">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <!-- الصف الأول: وقت الإرسال + الموافقة -->
                                        <Grid Grid.Row="0" Margin="0,0,0,12">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- وقت وتاريخ الإرسال -->
                                            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,10,0">
                                                <TextBlock Text="📅 وقت وتاريخ إرسال الاستمارة:" FontWeight="Bold" FontSize="12"
                                                     Foreground="#1E40AF" VerticalAlignment="Center" Width="150"/>
                                                <Border BorderBrush="#3B82F6" BorderThickness="1" Padding="6,3" Background="White" CornerRadius="3">
                                                    <TextBlock Text="{Binding ReportData.FormattedSubmissionTime, FallbackValue=11:05:02 - 2025/01/24}"
                                                         FontSize="11" FontWeight="Bold" Foreground="#1E40AF"/>
                                                </Border>
                                            </StackPanel>

                                            <!-- الموافقة على السفر -->
                                            <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="10,0,0,0">
                                                <TextBlock Text="🔐 النزول الميداني تم بموافقة:" FontWeight="Bold" FontSize="12"
                                                     Foreground="#DC2626" VerticalAlignment="Center" Width="150"/>
                                                <Border BorderBrush="#DC2626" BorderThickness="1" Padding="6,3" Background="White" CornerRadius="3">
                                                    <TextBlock Text="{Binding ReportData.ApprovalBy, FallbackValue=مدير الفرع}"
                                                         FontWeight="Bold" Foreground="#DC2626" FontSize="11"/>
                                                </Border>
                                            </StackPanel>
                                        </Grid>

                                        <!-- الصف الثاني: عنوان البيانات الأساسية -->
                                        <TextBlock Grid.Row="1" Text="البيانات الأساسية للزيارة" FontWeight="Bold" FontSize="14"
                                             Foreground="#2C3E50" Margin="0,0,0,8" HorizontalAlignment="Center"/>

                                        <!-- الصف الثالث: البيانات الأساسية في عمودين -->
                                        <Grid Grid.Row="2">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                            </Grid.RowDefinitions>

                                            <!-- العمود الأول -->
                                            <!-- رقم الزيارة -->
                                            <StackPanel Grid.Row="0" Grid.Column="0" Orientation="Horizontal" Margin="0,0,10,6">
                                                <TextBlock Text="رقم الزيارة:" FontWeight="Bold" Width="80" VerticalAlignment="Center" FontSize="11"/>
                                                <Border BorderBrush="#CCCCCC" BorderThickness="1" Padding="5,2" Background="White" Width="70">
                                                    <TextBlock Text="{Binding OdkVisitNumber, FallbackValue=2}"
                                                         FontWeight="Bold" Foreground="#2C3E50" HorizontalAlignment="Center" FontSize="11"/>
                                                </Border>
                                            </StackPanel>

                                            <!-- تاريخ البداية -->
                                            <StackPanel Grid.Row="1" Grid.Column="0" Orientation="Horizontal" Margin="0,0,10,6">
                                                <TextBlock FontWeight="Bold" Width="80" VerticalAlignment="Center" FontSize="11"><Run Text="تاريخ "/><Run FlowDirection="RightToLeft" Language="ar-ye" Text="النزول"/><Run Text=":"/></TextBlock>
                                                <Border BorderBrush="#CCCCCC" BorderThickness="1" Padding="5,2" Background="White" Width="90">
                                                    <TextBlock Text="{Binding ReportData.StartDate, FallbackValue=17/09/2025}"
                                                         FontWeight="Bold" Foreground="#2C3E50" HorizontalAlignment="Center" FontSize="11"/>
                                                </Border>
                                            </StackPanel>

                                            <!-- عدد الأيام -->
                                            <StackPanel Grid.Row="2" Grid.Column="0" Orientation="Horizontal" Margin="0,0,10,0">
                                                <TextBlock Text="عدد الأيام:" FontWeight="Bold" Width="80" VerticalAlignment="Center" FontSize="11"/>
                                                <Border BorderBrush="#CCCCCC" BorderThickness="1" Padding="5,2" Background="White" Width="50">
                                                    <TextBlock Text="{Binding ReportData.DaysCount, FallbackValue=3}"
                                                         FontWeight="Bold" Foreground="#2C3E50" HorizontalAlignment="Center" FontSize="11"/>
                                                </Border>
                                            </StackPanel>

                                            <!-- العمود الثاني -->
                                            <!-- القطاع -->
                                            <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal" Margin="10,0,0,6">
                                                <TextBlock Text="القطاع:" FontWeight="Bold" Width="60" VerticalAlignment="Center" FontSize="11"/>
                                                <Border BorderBrush="#CCCCCC" BorderThickness="1" Padding="5,2" Background="White" Width="150">
                                                    <TextBlock Text="{Binding ReportData.SectorName, FallbackValue=النقد مقابل العمل}"
                                                         FontWeight="Bold" Foreground="#2C3E50" FontSize="11"/>
                                                </Border>
                                            </StackPanel>

                                            <!-- تاريخ النهاية -->
                                            <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Horizontal" Margin="10,0,0,6">
                                                <TextBlock FontWeight="Bold" Width="60" VerticalAlignment="Center" FontSize="11"><Run Text="تاريخ "/><Run FlowDirection="RightToLeft" Language="ar-ye" Text="العودة"/><Run Text=":"/></TextBlock>
                                                <Border BorderBrush="#CCCCCC" BorderThickness="1" Padding="5,2" Background="White" Width="90">
                                                    <TextBlock Text="{Binding ReportData.EndDate, FallbackValue=19/09/2025}"
                                                         FontWeight="Bold" Foreground="#2C3E50" HorizontalAlignment="Center" FontSize="11"/>
                                                </Border>
                                            </StackPanel>

                                            <!-- عدد الزوار -->
                                            <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal" Margin="10,0,0,0">
                                                <TextBlock Text="عدد الزوار:" FontWeight="Bold" Width="60" VerticalAlignment="Center" FontSize="11"/>
                                                <Border BorderBrush="#CCCCCC" BorderThickness="1" Padding="5,2" Background="White" Width="50">
                                                    <TextBlock Text="{Binding ReportData.VisitorsCount, FallbackValue=2}"
                                                         FontWeight="Bold" Foreground="#2C3E50" HorizontalAlignment="Center" FontSize="11"/>
                                                </Border>
                                            </StackPanel>
                                        </Grid>
                                    </Grid>
                                </Border>

                                <!-- القسم الرابع: مهمة النزول -->
                                <Border Grid.Row="3" BorderBrush="#DDDDDD" BorderThickness="1" Margin="0,0,0,10" Background="#F8F9FA" Padding="15">
                                    <StackPanel>
                                        <TextBlock Text="مهمة النزول" FontWeight="Bold" FontSize="14"
                                             Foreground="#2C3E50" Margin="0,0,0,10"/>
                                        <Border BorderBrush="#CCCCCC" BorderThickness="1" Padding="10" Background="White">
                                            <TextBlock Text="{Binding ReportData.MissionPurpose, FallbackValue=مراجعة وتقييم المشاريع الميدانية}"
                                                 TextWrapping="Wrap" FontSize="12" Foreground="#2C3E50" FontWeight="Bold"/>
                                        </Border>
                                    </StackPanel>
                                </Border>

                                <!-- القسم الخامس: القائمين بالزيارة -->
                                <Border Grid.Row="4" BorderBrush="#DDDDDD" BorderThickness="1" Margin="0,0,0,10" Background="#F8F9FA" Padding="15">
                                    <StackPanel>
                                        <TextBlock Text="القائمين بالزيارة" FontWeight="Bold" FontSize="14"
                                             Foreground="#2C3E50" Margin="0,0,0,10"/>
                                        <Border BorderBrush="#CCCCCC" BorderThickness="1" Background="White">
                                            <ItemsControl ItemsSource="{Binding ReportData.Visitors}">
                                                <ItemsControl.ItemTemplate>
                                                    <DataTemplate>
                                                        <Border BorderBrush="#EEEEEE" BorderThickness="0,0,0,1" Padding="10,8">
                                                            <StackPanel Orientation="Horizontal">
                                                                <TextBlock Text="•" FontWeight="Bold" Margin="0,0,8,0" Foreground="#2C3E50"/>
                                                                <TextBlock Text="{Binding Name}" FontWeight="Bold" Margin="0,0,10,0" Foreground="#2C3E50"/>
                                                                <TextBlock Text="-" Margin="0,0,5,0" Foreground="#666666"/>
                                                                <TextBlock Text="{Binding Rank}" Foreground="#666666"/>
                                                            </StackPanel>
                                                        </Border>
                                                    </DataTemplate>
                                                </ItemsControl.ItemTemplate>
                                            </ItemsControl>
                                        </Border>
                                    </StackPanel>
                                </Border>

                                <!-- القسم السادس: المشاريع وخط السير -->
                                <Border Grid.Row="5" BorderBrush="#DDDDDD" BorderThickness="1" Margin="0,0,0,10" Background="#F8F9FA" Padding="15">
                                    <StackPanel>
                                        <TextBlock Text="المشاريع وخط السير" FontWeight="Bold" FontSize="14"
                                             Foreground="#2C3E50" Margin="0,0,0,10"/>

                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- المشاريع -->
                                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                                <TextBlock Text="المشاريع:" FontWeight="Bold" FontSize="12"
                                                     Foreground="#2C3E50" Margin="0,0,0,5"/>
                                                <Border BorderBrush="#CCCCCC" BorderThickness="1" Background="White" MinHeight="100">
                                                    <ItemsControl ItemsSource="{Binding ReportData.Projects}">
                                                        <ItemsControl.ItemTemplate>
                                                            <DataTemplate>
                                                                <Border BorderBrush="#EEEEEE" BorderThickness="0,0,0,1" Padding="8,5">
                                                                    <StackPanel>
                                                                        <TextBlock Text="{Binding ProjectNumber}" FontSize="10" Foreground="#666666" TextWrapping="Wrap" FlowDirection="RightToLeft" HorizontalAlignment="Right"/>
                                                                        <TextBlock Text="{Binding ProjectName}" FontSize="10" Foreground="#666666" TextWrapping="Wrap" FlowDirection="RightToLeft" HorizontalAlignment="Right"/>
                                                                    </StackPanel>
                                                                </Border>
                                                            </DataTemplate>
                                                        </ItemsControl.ItemTemplate>
                                                    </ItemsControl>
                                                </Border>
                                            </StackPanel>

                                            <!-- خط السير -->
                                            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                                <TextBlock Text="خط السير:" FontWeight="Bold" FontSize="12"
                                                     Foreground="#2C3E50" Margin="0,0,0,5"/>
                                                <Border BorderBrush="#CCCCCC" BorderThickness="1" Background="White" MinHeight="100">
                                                    <ItemsControl ItemsSource="{Binding ReportData.Itinerary}">
                                                        <ItemsControl.ItemTemplate>
                                                            <DataTemplate>
                                                                <Border BorderBrush="#EEEEEE" BorderThickness="0,0,0,1" Padding="8,5">
                                                                    <StackPanel Orientation="Horizontal">
                                                                        <TextBlock Text="{Binding DayNumber}" FontWeight="Bold" FontSize="10"
                                                                             Foreground="#2C3E50" Margin="0,0,5,0"/>
                                                                        <TextBlock Text="-" Margin="0,0,5,0" Foreground="#666666"/>
                                                                        <TextBlock Text="{Binding Plan}" FontSize="10" Foreground="#666666" TextWrapping="Wrap"/>
                                                                    </StackPanel>
                                                                </Border>
                                                            </DataTemplate>
                                                        </ItemsControl.ItemTemplate>
                                                    </ItemsControl>
                                                </Border>
                                            </StackPanel>
                                        </Grid>

                                        <!-- الملاحظات أسفل المشاريع وخط السير -->
                                        <Border BorderBrush="#DDDDDD" BorderThickness="1" Margin="0,15,0,0" Background="#F8F9FA" Padding="15">
                                            <StackPanel>
                                                <TextBlock Text="ملاحظات الزيارة" FontWeight="Bold" FontSize="14"
                                                     Foreground="#2C3E50" Margin="0,0,0,10"/>
                                                <Border BorderBrush="#CCCCCC" BorderThickness="1" Padding="10" Background="White">
                                                    <TextBlock Text="{Binding ReportData.Notes, FallbackValue=لا توجد ملاحظات إضافية}"
                                                         TextWrapping="Wrap" FontSize="12" Foreground="#2C3E50" FontWeight="Bold"/>
                                                </Border>
                                            </StackPanel>
                                        </Border>
                                    </StackPanel>
                                </Border>

                                <!-- القسم السابع: القائم بالزيارة للتوقيع -->
                                <Grid Grid.Row="6" Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="300"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- مساحة فارغة على اليمين -->
                                    <StackPanel Grid.Column="0"/>

                                    <!-- القائم بالزيارة على اليسار -->
                                    <StackPanel HorizontalAlignment="Left" Margin="71,0,0,0">
                                        <TextBlock Text="القائم بالزيارة الميدانية" FontWeight="Bold" FontSize="14"
                                             Foreground="#2C3E50" Margin="0,0,0,10" HorizontalAlignment="Center"/>
                                        <TextBlock Text="{Binding ReportData.VisitConductor, FallbackValue=لم يتم تحديد القائم بالزيارة}"
                                             FontWeight="Bold" FontSize="12" Foreground="#2C3E50" TextWrapping="Wrap"
                                             HorizontalAlignment="Center" TextAlignment="Center" Margin="0,0,0,30"/>
                                        <TextBlock Text="التوقيع" FontWeight="Bold" FontSize="12"
                                             Foreground="#2C3E50" Margin="0,10,0,0" HorizontalAlignment="Center"/>
                                    </StackPanel>
                                    <StackPanel HorizontalAlignment="Left" Margin="107,0,0,0" Grid.Column="1">
                                        <TextBlock FontWeight="Bold" FontSize="14"
                                            Foreground="#2C3E50" Margin="0,0,0,10" HorizontalAlignment="Center"><Run FlowDirection="RightToLeft" Text=" "/><Run FlowDirection="RightToLeft" Language="ar-ye" Text="مسئــــول "/><Run FlowDirection="RightToLeft" Text="الحركة"/></TextBlock>
                                        <TextBlock
                                            FontWeight="Bold" FontSize="12" Foreground="#2C3E50" TextWrapping="Wrap"
                                            HorizontalAlignment="Center" TextAlignment="Center" Margin="0,0,0,30"><Run FlowDirection="RightToLeft" Language="ar-ye" Text="علي علي العمدي"/></TextBlock>
                                        <TextBlock Text="التوقيع" FontWeight="Bold" FontSize="12"
                                            Foreground="#2C3E50" Margin="0,10,0,0" HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Grid>

                                <!-- رقم الصفحة -->
                            </Grid>

                        </StackPanel>
                    </Border>

                    <!-- فاصل بين الصفحات -->
                    <Border Height="30" Background="Transparent" Margin="0,10,0,10"/>

                    <!-- الصفحة الثانية: محضر استخراج عروض الأسعار -->
                    <Border Style="{StaticResource PrintPageStyle}" Height="948" Width="730">
                        <StackPanel Margin="7,5,5,-22">

                            <!-- Compact Professional Header -->
                            <Grid Margin="0,0,0,10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="2*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- Left Side - Organization Header -->
                                <StackPanel Grid.Column="0" HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <TextBlock Text="الجمهورية اليمنية" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#333333"/>
                                    <TextBlock Text="الصندوق الاجتماعي للتنمية" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#1E3A8A" Margin="0,2,0,0"/>
                                    <TextBlock Text="فرع ذمار والبيضاء" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#333333"/>
                                </StackPanel>

                                <!-- Center - Main Title -->
                                <Border Grid.Column="1" Background="#E8F4FD" BorderBrush="#4682B4" BorderThickness="2" Padding="20,10" HorizontalAlignment="Center">
                                    <TextBlock Text="محضر استدراج عروض اسعار" FontSize="20" FontWeight="Bold"
                                 HorizontalAlignment="Center" Foreground="#1E3A8A" TextAlignment="Center"/>
                                </Border>

                                <!-- Right Side - Date and Visit Number with Icons -->
                                <StackPanel Grid.Column="2" HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <Border Background="#F0F8FF" BorderBrush="#4682B4" BorderThickness="1" Padding="8,4" Margin="0,0,0,4">
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <TextBlock Text="📅" FontSize="12" Margin="0,0,5,0" VerticalAlignment="Center"/>
                                            <TextBlock FontSize="10" FontWeight="Bold" Foreground="#333333" VerticalAlignment="Center">
                                            <Run Text="التاريخ: "/>
                                            <Run Text="{Binding ReportData.ReportDate, FallbackValue=24/06/2025}"/>
                                            </TextBlock>
                                        </StackPanel>
                                    </Border>
                                    <Border Background="#F0FFF0" BorderBrush="#228B22" BorderThickness="1" Padding="8,4">
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <TextBlock Text="🔢" FontSize="12" Margin="0,0,5,0" VerticalAlignment="Center"/>
                                            <TextBlock FontSize="10" FontWeight="Bold" Foreground="#333333" VerticalAlignment="Center">
                                            <Run Text="رقم الزيارة: "/>
                                            <Run Text="{Binding ReportData.VisitNumber, FallbackValue=3333}"/>
                                            </TextBlock>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </Grid>

                            <!-- Compact Projects Section -->
                            <Border BorderBrush="#333333" BorderThickness="1" Margin="0,0,0,10" Background="White">
                                <StackPanel>
                                    <!-- Compact Header with Sector -->
                                    <Border Background="#F8F9FA" Padding="8,6">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- Projects Title on the left -->
                                            <StackPanel Grid.Column="0" HorizontalAlignment="Left">
                                                <TextBlock Text="المشاريع التي سيتم زيارتها"
                                                 FontWeight="Bold" FontSize="12" HorizontalAlignment="Left" Foreground="Black"/>
                                            </StackPanel>

                                            <!-- Empty space in center -->
                                            <TextBlock Grid.Column="1"/>

                                            <!-- Sector on the right with Icon -->
                                            <Border Grid.Column="2" BorderBrush="#DDDDDD" BorderThickness="1" Padding="8,5" Background="White">
                                                <TextBlock HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="12" FontWeight="Bold" Foreground="Black"><Run Text="🏢 القطاع: "/><Run Text=" "/><Run Text="{Binding ReportData.SectorName, FallbackValue=الصحة والحماية الاجتماعية}"/></TextBlock>
                                            </Border>
                                        </Grid>
                                    </Border>

                                    <!-- Enhanced Table Header -->
                                    <Border BorderBrush="Black" BorderThickness="2" Background="#E8F4FD" Height="36">
                                        <Grid MinHeight="35">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="120"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="80"/>
                                            </Grid.ColumnDefinitions>

                                            <Border Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="8,8">
                                                <TextBlock Text="رقم المشروع" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                            </Border>
                                            <Border Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="8,8">
                                                <TextBlock Text="اسم المشروع" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                            </Border>
                                            <Border Grid.Column="2" Padding="8,8">
                                                <TextBlock Text="عدد الأيام" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                            </Border>
                                        </Grid>
                                    </Border>

                                    <!-- Enhanced Table Data -->
                                    <ItemsControl ItemsSource="{Binding ReportData.Projects}">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <Border BorderBrush="Black" BorderThickness="2,0,2,1">
                                                    <Grid MinHeight="35" Background="White">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="120"/>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="80"/>
                                                        </Grid.ColumnDefinitions>

                                                        <Border Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="8,6">
                                                            <TextBlock Text="{Binding ProjectNumber}" HorizontalAlignment="Right"
                                                             VerticalAlignment="Center" TextWrapping="Wrap" FontSize="11" Foreground="Black" FlowDirection="RightToLeft"/>
                                                        </Border>
                                                        <Border Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="8,6">
                                                            <TextBlock Text="{Binding ProjectName}" HorizontalAlignment="Right"
                                                             VerticalAlignment="Center" TextWrapping="Wrap"
                                                             FontSize="11" LineHeight="16" Foreground="Black" FlowDirection="RightToLeft"/>
                                                        </Border>
                                                        <Border Grid.Column="2" Padding="8,6">
                                                            <TextBlock Text="{Binding ProjectDays}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" FontSize="11" FontWeight="Bold" Foreground="Black"/>
                                                        </Border>
                                                    </Grid>
                                                </Border>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </StackPanel>
                            </Border>

                            <!-- Compact Visit Data Section -->
                            <Border BorderBrush="#333333" BorderThickness="1" Margin="0,0,0,10" Background="White">
                                <StackPanel>
                                    <!-- Compact Header -->
                                    <Border Background="#4682B4" Padding="8,6">
                                        <TextBlock Text="بيانات الزيارة الميدانية" FontWeight="Bold" FontSize="11"
                                         Foreground="White" HorizontalAlignment="Center"/>
                                    </Border>

                                    <!-- Compact Content -->
                                    <StackPanel Margin="10,8">
                                        <!-- Activity Nature -->
                                        <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="8" Padding="15,12" Margin="0,0,0,12">
                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock Text="🎯" FontSize="14" Margin="0,0,10,0" VerticalAlignment="Top"/>
                                                <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="18" VerticalAlignment="Top" MaxWidth="600"><Run Text="طبيعة النشاط: " FontWeight="Bold" Foreground="Black"/><Run Text=" "/><Run Text="{Binding ReportData.VisitNature, FallbackValue=اضافة نشاط المهمة}" Foreground="Black"/></TextBlock>
                                            </StackPanel>
                                        </Border>

                                        <!-- Visit Conductor -->
                                        <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="8" Padding="15,12" Margin="0,0,0,10">
                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock Text="👤" FontSize="14" Margin="0,0,10,0" VerticalAlignment="Top"/>
                                                <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="18" VerticalAlignment="Top" MaxWidth="600"><Run Text="القائم بالزيارة: " FontWeight="Bold" Foreground="Black"/><Run Text=" "/><Run Text="{Binding ReportData.VisitConductor, FallbackValue=' '}" Foreground="Black"/></TextBlock>
                                            </StackPanel>
                                        </Border>



                                        <!-- Route and Message Frame -->
                                        <Border Margin="0,5,0,5" BorderBrush="Black" BorderThickness="2" Background="White">
                                            <StackPanel>
                                                <!-- Message Header -->
                                                <Border Background="#E8F4FD" Padding="8,6">
                                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                                                        <TextBlock Text="🗺️" FontSize="12" Margin="0,0,8,0" Foreground="Black" VerticalAlignment="Center"/>
                                                        <TextBlock Text="خط السير ونص الرسالة المرسلة للسائقين" FontSize="12" FontWeight="Bold"
                                                         Foreground="Black" VerticalAlignment="Center"/>
                                                    </StackPanel>
                                                </Border>

                                                <!-- Message Content -->
                                                <Border Background="White" Padding="10,8">
                                                    <Border BorderBrush="#CCCCCC" BorderThickness="1" Padding="8" Background="White">
                                                        <TextBlock FontSize="11" TextWrapping="Wrap" LineHeight="16" Foreground="Black"
                                                         Text="{Binding ReportData.WinnerDriverMessage, FallbackValue=لم يتم حفظ نص الرسالة للسائق الفائز}"
                                                         TextAlignment="Left" FontFamily="Segoe UI"/>
                                                    </Border>
                                                </Border>
                                            </StackPanel>
                                        </Border>

                                        <!-- Enhanced Dates Section in Single Row -->
                                        <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="8" Padding="15,12" Margin="0,0,0,8">
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                <!-- Departure Date -->
                                                <StackPanel Orientation="Horizontal" Margin="0,0,30,0">
                                                    <TextBlock Text="📅" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                                    <TextBlock Text="تاريخ النزول:" FontWeight="Bold" FontSize="12" Foreground="Black" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                                    <TextBlock Text="{Binding ReportData.DepartureDate, FallbackValue=15/06/2025}"
                                                     FontSize="12" FontWeight="Bold" Foreground="Black" VerticalAlignment="Center"/>
                                                </StackPanel>

                                                <!-- Return Date -->
                                                <StackPanel Orientation="Horizontal" Margin="0,0,30,0">
                                                    <TextBlock Text="🔄" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                                    <TextBlock Text="تاريخ العودة:" FontWeight="Bold" FontSize="12" Foreground="Black" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                                    <TextBlock Text="{Binding ReportData.ReturnDate, FallbackValue=17/06/2025}"
                                                     FontSize="12" FontWeight="Bold" Foreground="Black" VerticalAlignment="Center"/>
                                                </StackPanel>

                                                <!-- Days Count -->
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="⏰" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                                    <TextBlock Text="عدد الأيام:" FontWeight="Bold" FontSize="12" Foreground="Black" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                                    <TextBlock Text="{Binding ReportData.DaysCount, FallbackValue=3}"
                                                     FontSize="12" FontWeight="Bold" Foreground="Black" VerticalAlignment="Center"/>
                                                    <TextBlock Text=" يوم" FontSize="12" FontWeight="Bold" Foreground="Black" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                                </StackPanel>
                                            </StackPanel>
                                        </Border>

                                        <!-- Enhanced Notes Section -->
                                        <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="8" Padding="15,12" Margin="0,8,0,10">
                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock Text="📝" FontSize="14" Margin="0,0,10,0" VerticalAlignment="Top"/>
                                                <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="18" VerticalAlignment="Top" MaxWidth="600"><Run Text="ملاحظات الزيارة: " FontWeight="Bold" Foreground="Black"/><Run Text=" "/><Run Text="{Binding ReportData.Notes, FallbackValue=لا توجد ملاحظات إضافية}" Foreground="Black"/></TextBlock>
                                            </StackPanel>
                                        </Border>
                                    </StackPanel>
                                </StackPanel>
                            </Border>

                            <!-- Compact Price Offers Table -->
                            <Border BorderBrush="#333333" BorderThickness="1" Margin="0,0,0,0" Background="White">
                                <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" MaxHeight="400">
                                    <StackPanel>
                                        <!-- Enhanced Header -->
                                        <Border Background="#B8D4F0" Padding="10,8">
                                            <TextBlock Text="قائمة الأسعار المقدمة من السائقين" FontWeight="Bold" FontSize="14"
                                         Foreground="Black" HorizontalAlignment="Center"/>
                                        </Border>

                                        <!-- Enhanced Table Header -->
                                        <Border BorderBrush="Black" BorderThickness="2" Background="#F0F8FF">
                                            <Grid MinHeight="25">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="60"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="120"/>
                                                    <ColumnDefinition Width="100"/>
                                                    <ColumnDefinition Width="80"/>
                                                </Grid.ColumnDefinitions>

                                                <Border Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="5">
                                                    <TextBlock Text="الرقم" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                                </Border>
                                                <Border Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="5">
                                                    <TextBlock Text="اسم السائق" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                                </Border>
                                                <Border Grid.Column="2" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="5">
                                                    <TextBlock Text="رقم التلفون" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                                </Border>
                                                <Border Grid.Column="3" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="5">
                                                    <TextBlock Text="السعر المقدم" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                                </Border>
                                                <Border Grid.Column="4" Padding="5">
                                                    <TextBlock Text="الحالة" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                                </Border>
                                            </Grid>
                                        </Border>

                                        <!-- Enhanced Table Data -->
                                        <ItemsControl ItemsSource="{Binding ReportData.PriceOffers}">
                                            <!-- Empty State Message -->
                                            <ItemsControl.Style>
                                                <Style TargetType="ItemsControl">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding ReportData.PriceOffers.Count}" Value="0">
                                                            <Setter Property="Template">
                                                                <Setter.Value>
                                                                    <ControlTemplate>
                                                                        <Border Background="#F8F9FA" Padding="20" Margin="2">
                                                                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                                                                <TextBlock Text="📋" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                                                                <TextBlock Text="لا توجد عروض أسعار لهذه الزيارة"
                                                                                 FontSize="14" FontWeight="SemiBold"
                                                                                 HorizontalAlignment="Center" Foreground="#6C757D" Margin="0,0,0,5"/>
                                                                                <TextBlock Text="يرجى إضافة عروض الأسعار من نافذة الرسائل المهنية"
                                                                                 FontSize="12" HorizontalAlignment="Center"
                                                                                 Foreground="#6C757D" TextWrapping="Wrap" MaxWidth="300"/>
                                                                            </StackPanel>
                                                                        </Border>
                                                                    </ControlTemplate>
                                                                </Setter.Value>
                                                            </Setter>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </ItemsControl.Style>
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate>
                                                    <Border BorderBrush="Black">
                                                        <Border.Style>
                                                            <Style TargetType="Border">
                                                                <Setter Property="BorderThickness" Value="2,0,2,1"/>
                                                                <Style.Triggers>
                                                                    <!-- إضافة حد سفلي للعنصر الأخير -->
                                                                    <DataTrigger Binding="{Binding RelativeSource={RelativeSource PreviousData}}" Value="{x:Null}">
                                                                        <Setter Property="BorderThickness" Value="2,0,2,2"/>
                                                                    </DataTrigger>
                                                                </Style.Triggers>
                                                            </Style>
                                                        </Border.Style>
                                                        <Grid MinHeight="22" Background="White">
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="60"/>
                                                                <ColumnDefinition Width="*"/>
                                                                <ColumnDefinition Width="120"/>
                                                                <ColumnDefinition Width="100"/>
                                                                <ColumnDefinition Width="80"/>
                                                            </Grid.ColumnDefinitions>

                                                            <!-- Serial Number -->
                                                            <Border Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="3">
                                                                <TextBlock Text="{Binding SerialNumber}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" FontSize="11" FontWeight="Bold" Foreground="Black"/>
                                                            </Border>

                                                            <!-- Driver Name -->
                                                            <Border Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="3">
                                                                <TextBlock Text="{Binding DriverName}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" FontSize="11" FontWeight="SemiBold" Foreground="Black"/>
                                                            </Border>

                                                            <!-- Phone Number -->
                                                            <Border Grid.Column="2" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="3">
                                                                <TextBlock Text="{Binding PhoneNumber}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" FontSize="10" FontFamily="Consolas" Foreground="Black"/>
                                                            </Border>

                                                            <!-- Price -->
                                                            <Border Grid.Column="3" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="3">
                                                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                                    <TextBlock Text="{Binding OfferedPrice, StringFormat=\{0:N0\}}"
                                                                 FontSize="11" FontWeight="Bold" Foreground="Black" VerticalAlignment="Center"/>
                                                                    <TextBlock Text=" ريال" FontSize="10" Foreground="Black" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                                                </StackPanel>
                                                            </Border>

                                                            <!-- Status -->
                                                            <Border Grid.Column="4" Padding="3">
                                                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                                    <TextBlock FontSize="10" FontWeight="Bold" VerticalAlignment="Center">
                                                                        <TextBlock.Style>
                                                                            <Style TargetType="{x:Type TextBlock}">
                                                                                <Setter Property="Text" Value="{Binding Status}"/>
                                                                                <Setter Property="Foreground" Value="Black"/>
                                                                                <Style.Triggers>
                                                                                    <!-- حالة الفوز -->
                                                                                    <DataTrigger Binding="{Binding IsWinner}" Value="True">
                                                                                        <Setter Property="Text" Value="🏆 فائز"/>
                                                                                        <Setter Property="Foreground" Value="#28A745"/>
                                                                                    </DataTrigger>
                                                                                    <!-- حالة مقبول -->
                                                                                    <DataTrigger Binding="{Binding Status}" Value="مقبول">
                                                                                        <Setter Property="Text" Value="✅ مقبول"/>
                                                                                        <Setter Property="Foreground" Value="#28A745"/>
                                                                                    </DataTrigger>
                                                                                    <!-- حالة مرفوض -->
                                                                                    <DataTrigger Binding="{Binding Status}" Value="مرفوض">
                                                                                        <Setter Property="Text" Value="❌ مرفوض"/>
                                                                                        <Setter Property="Foreground" Value="#DC3545"/>
                                                                                    </DataTrigger>
                                                                                    <!-- حالة تم التقديم -->
                                                                                    <DataTrigger Binding="{Binding Status}" Value="تم التقديم">
                                                                                        <Setter Property="Text" Value="📋 تم التقديم"/>
                                                                                        <Setter Property="Foreground" Value="#007BFF"/>
                                                                                    </DataTrigger>
                                                                                    <!-- حالة موافق -->
                                                                                    <DataTrigger Binding="{Binding Status}" Value="موافق">
                                                                                        <Setter Property="Text" Value="✅ موافق"/>
                                                                                        <Setter Property="Foreground" Value="#28A745"/>
                                                                                    </DataTrigger>
                                                                                </Style.Triggers>
                                                                            </Style>
                                                                        </TextBlock.Style>
                                                                    </TextBlock>
                                                                </StackPanel>
                                                            </Border>
                                                        </Grid>
                                                    </Border>
                                                </DataTemplate>
                                            </ItemsControl.ItemTemplate>
                                        </ItemsControl>
                                    </StackPanel>
                                </ScrollViewer>
                            </Border>

                            <!-- Simplified Award Decision Section -->
                            <StackPanel Margin="0,0,0,5">
                                <!-- Decision Text and Driver Details in Single Section -->
                                <Border Background="White" Padding="0,0,0,14" Margin="0,0,0,0" Height="77" Width="686">
                                    <StackPanel Margin="0,0,14,-14">
                                        <!-- Decision Text with Driver Name - Flexible Layout -->
                                        <TextBlock HorizontalAlignment="Right" Margin="0,0,0,5" Foreground="Black" TextWrapping="Wrap" LineHeight="20" FontWeight="Bold" Width="669" FontSize="11">
                                        <Run Text="بناءً على الأسعار المقدمة من الإخوة أصحاب المركبات الذي تم التواصل معهم أعلاه، فقد تم إرساء النقل على الأخ "/>
                                        <Run Text="{Binding ReportData.WinnerDriver.DriverName}" FontWeight="Bold" FontSize="14"/>
                                        </TextBlock>

                                        <!-- Winner Driver Details in Distinguished Frame - Same Line -->
                                        <Border Background="#F0F8FF" BorderBrush="#4682B4" BorderThickness="2" CornerRadius="8" Padding="15,10" Margin="0,0,0,0" Height="40">
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                <!-- ID -->
                                                <StackPanel Orientation="Horizontal" Margin="0,0,25,0">
                                                    <TextBlock Text="🆔" FontSize="16" Margin="0,0,6,0" VerticalAlignment="Center"/>
                                                    <TextBlock Text="رقم البطاقة:" FontWeight="Bold" Foreground="Black" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                                    <TextBlock Text="{Binding ReportData.WinnerDriver.NationalId}" FontSize="13" FontWeight="Bold"
                                                     Foreground="Black" VerticalAlignment="Center"/>
                                                </StackPanel>

                                                <!-- Vehicle Type -->
                                                <StackPanel Orientation="Horizontal" Margin="0,0,25,0">
                                                    <TextBlock Text="🚙" FontSize="16" Margin="0,0,6,0" VerticalAlignment="Center"/>
                                                    <TextBlock Text="نوع السيارة:" FontWeight="Bold" Foreground="Black" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                                    <TextBlock Text="{Binding ReportData.WinnerDriver.VehicleType}" FontSize="13" FontWeight="Bold"
                                                     Foreground="Black" VerticalAlignment="Center"/>
                                                </StackPanel>

                                                <!-- Capacity -->
                                                <StackPanel Orientation="Horizontal" Margin="0,0,25,0">
                                                    <TextBlock Text="⚖️" FontSize="16" Margin="0,0,6,0" VerticalAlignment="Center"/>
                                                    <TextBlock Text="قدرة السيارة:" FontWeight="Bold" Foreground="Black" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                                    <TextBlock Text="{Binding ReportData.WinnerDriver.VehicleCapacity}" FontSize="13" FontWeight="Bold"
                                                     Foreground="Black" VerticalAlignment="Center"/>
                                                </StackPanel>

                                                <!-- Year -->
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="📅" FontSize="16" Margin="0,0,6,0" VerticalAlignment="Center"/>
                                                    <TextBlock Text="سنة الصنع:" FontWeight="Bold" Foreground="Black" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                                    <TextBlock Text="{Binding ReportData.WinnerDriver.ManufactureYear}" FontSize="13" FontWeight="Bold"
                                                     Foreground="Black" VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </StackPanel>
                                        </Border>
                                    </StackPanel>
                                </Border>
                            </StackPanel>


                            <!-- Professional Signatures Section -->
                            <Border Padding="10,0" Margin="0,0,0,0" Height="106">
                                <StackPanel>
                                    <Grid Height="498">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="23*"/>
                                            <ColumnDefinition Width="84*"/>
                                            <ColumnDefinition Width="106*"/>
                                            <ColumnDefinition Width="109*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Task Manager -->
                                        <StackPanel Grid.Column="0" HorizontalAlignment="Center" Margin="0,0,0,339" Grid.ColumnSpan="2" Width="172">
                                            <TextBlock Text="المكلف بالمهمة" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,20"/>
                                            <TextBlock HorizontalAlignment="Center" FontSize="12" FontWeight="Bold" TextWrapping="Wrap" MaxWidth="180" Foreground="Black" LineHeight="18"
                                             Text="{Binding ReportData.VisitConductorFormatted, FallbackValue=جمال علي عبدالله الفاطمي &amp; أحمد صالح أحمد حميد, Mode=OneWay}"/>
                                        </StackPanel>

                                        <!-- Movement Responsible -->
                                        <StackPanel Grid.Column="2" HorizontalAlignment="Center" Margin="0,0,0,339" Width="84">
                                            <TextBlock Text="مسئول الحركة" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,20"/>
                                            <TextBlock Text="علي علي العمدي" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold" Foreground="Black"/>
                                        </StackPanel>

                                        <!-- Branch Manager -->
                                        <StackPanel Grid.Column="3" HorizontalAlignment="Center" Margin="0,0,0,395" Width="118">
                                            <TextBlock Text="يعتمد" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,35"/>
                                            <TextBlock Text="م/محمد محمد الديلمي" FontSize="12" FontWeight="Bold" Foreground="Black" Margin="0,2,2,0" />
                                            <TextBlock Text="مدير الفرع" HorizontalAlignment="Center" FontSize="11" FontStyle="Italic" Foreground="Black" Margin="0,2,0,0"/>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </Border>

                            <!-- توقيعات الصفحة الأولى -->
                            <StackPanel Margin="0,20,0,10" Height="100" RenderTransformOrigin="0.5,0.5">
                                <StackPanel.RenderTransform>
                                    <TransformGroup>
                                        <ScaleTransform ScaleY="-1"/>
                                        <SkewTransform/>
                                        <RotateTransform/>
                                        <TranslateTransform/>
                                    </TransformGroup>
                                </StackPanel.RenderTransform>
                            </StackPanel>

                            <!-- رقم الصفحة الثانية -->

                        </StackPanel>
                    </Border>

                    <!-- فاصل بين الصفحات -->
                    <Border Height="30" Background="Transparent" Margin="0,10,0,10"/>

                    <!-- الصفحة الثانية: توثيق النزول الميداني -->
                    <Border Style="{StaticResource PrintPageStyle}" Width="734" Height="1021">
                        <StackPanel Margin="20" TextElement.FontFamily="Arial">

                            <!-- رأس الصفحة الاحترافي -->
                            <!-- خط فاصل تحت القطاع -->
                            <!-- رأس الصفحة الاحترافي -->
                            <Grid Height="65">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="150"/>
                                </Grid.ColumnDefinitions>

                                <!-- الشعار والقطاع على اليسار -->
                                <StackPanel Grid.Column="0" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="10,0,0,0">
                                    <Image Source="C:\Users\<USER>\Desktop\sys\icons\sfd.png"
                                       Width="100" Height="57"
                                       Stretch="Uniform"
                                       />
                                </StackPanel>

                                <!-- العنوان الرئيسي في الوسط مع مربع -->
                                <Border Grid.Column="1"
                                    BorderBrush="Black" BorderThickness="2"
                                    Padding="15,10" Margin="0,1,0,0"
                                    HorizontalAlignment="Center" VerticalAlignment="Top" Height="50">
                                    <TextBlock Text="توثيق الرسائل النصية للنزول الميداني"
                                           FontSize="18" FontWeight="Bold"
                                           HorizontalAlignment="Center" VerticalAlignment="Center"
                                           Foreground="Black"
                                           TextAlignment="Center"/>
                                </Border>

                                <!-- رقم الزيارة على اليمين -->
                                <StackPanel Grid.Column="2" HorizontalAlignment="Right" VerticalAlignment="Top" Margin="0,20,10,0">
                                    <Border Background="#F0FFF0" BorderBrush="#228B22" BorderThickness="1" Padding="8,4">
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <TextBlock Text="🔢" FontSize="12" Margin="0,0,5,0" VerticalAlignment="Center"/>
                                            <TextBlock FontSize="10" FontWeight="Bold" Foreground="#333333" VerticalAlignment="Center"><Run Text="رقم الزيارة: "/><Run Text=" "/><Run Text="{Binding ReportData.VisitNumber, FallbackValue=3333}"/></TextBlock>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </Grid>
                            <!-- خط فاصل تحت القطاع -->
                            <Border Height="2" Background="#333333"/>

                            <!-- محتوى الصفحة - 4 أقسام للصور بالتساوي -->
                            <Grid Height="971" Margin="0,0,0,10">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- القسم الأول -->
                                <Border Grid.Row="0" Grid.Column="0" BorderBrush="#2C3E50" BorderThickness="1" Margin="5" Padding="3" CornerRadius="2" Background="White">
                                    <!-- مساحة للصورة الفعلية -->
                                    <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="2">
                                        <Grid>
                                            <!-- الصورة الفعلية إذا كانت متوفرة -->
                                            <Image x:Name="DocumentationImage1"
                                               Source="{Binding ReportData.DocumentationImage1}"
                                               Stretch="Fill"
                                               Visibility="{Binding ReportData.DocumentationImage1, Converter={StaticResource NullToVisibilityConverter}}"/>

                                            <!-- أيقونة افتراضية إذا لم تكن الصورة متوفرة -->
                                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center"
                                                  Visibility="{Binding ReportData.DocumentationImage1, ConverterParameter=Inverse, Converter={StaticResource NullToVisibilityConverter}}">
                                                <TextBlock Text="📷" FontSize="48" HorizontalAlignment="Center" Foreground="#6C757D"/>
                                                <TextBlock Text="لا توجد صورة" FontSize="12" HorizontalAlignment="Center" Foreground="#6C757D" Margin="0,5,0,0"/>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </Border>

                                <!-- القسم الثاني -->
                                <Border Grid.Row="0" Grid.Column="1" BorderBrush="#2C3E50" BorderThickness="1" Margin="5" Padding="3" CornerRadius="2" Background="White">
                                    <!-- مساحة للصورة الفعلية -->
                                    <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="2">
                                        <Grid>
                                            <!-- الصورة الفعلية إذا كانت متوفرة -->
                                            <Image x:Name="DocumentationImage2"
                                               Source="{Binding ReportData.DocumentationImage2}"
                                               Stretch="Fill"
                                               Visibility="{Binding ReportData.DocumentationImage2, Converter={StaticResource NullToVisibilityConverter}}"/>

                                            <!-- أيقونة افتراضية إذا لم تكن الصورة متوفرة -->
                                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center"
                                                  Visibility="{Binding ReportData.DocumentationImage2, ConverterParameter=Inverse, Converter={StaticResource NullToVisibilityConverter}}">
                                                <TextBlock Text="📷" FontSize="48" HorizontalAlignment="Center" Foreground="#6C757D"/>
                                                <TextBlock Text="لا توجد صورة" FontSize="12" HorizontalAlignment="Center" Foreground="#6C757D" Margin="0,5,0,0"/>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </Border>

                                <!-- القسم الثالث -->
                                <Border Grid.Row="1" Grid.Column="0" BorderBrush="#2C3E50" BorderThickness="1" Margin="5" Padding="3" CornerRadius="2" Background="White">
                                    <!-- مساحة للصورة الفعلية -->
                                    <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="2">
                                        <Grid>
                                            <!-- الصورة الفعلية إذا كانت متوفرة -->
                                            <Image x:Name="DocumentationImage3"
                                               Source="{Binding ReportData.DocumentationImage3}"
                                               Stretch="Fill"
                                               Visibility="{Binding ReportData.DocumentationImage3, Converter={StaticResource NullToVisibilityConverter}}"/>

                                            <!-- أيقونة افتراضية إذا لم تكن الصورة متوفرة -->
                                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center"
                                                  Visibility="{Binding ReportData.DocumentationImage3, ConverterParameter=Inverse, Converter={StaticResource NullToVisibilityConverter}}">
                                                <TextBlock Text="📷" FontSize="48" HorizontalAlignment="Center" Foreground="#6C757D"/>
                                                <TextBlock Text="لا توجد صورة" FontSize="12" HorizontalAlignment="Center" Foreground="#6C757D" Margin="0,5,0,0"/>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </Border>

                                <!-- القسم الرابع -->
                                <Border Grid.Row="1" Grid.Column="1" BorderBrush="#2C3E50" BorderThickness="1" Margin="5" Padding="3" CornerRadius="2" Background="White">
                                    <!-- مساحة للصورة الفعلية -->
                                    <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="2">
                                        <Grid>
                                            <!-- الصورة الفعلية إذا كانت متوفرة -->
                                            <Image x:Name="DocumentationImage4"
                                               Source="{Binding ReportData.DocumentationImage4}"
                                               Stretch="Fill"
                                               Visibility="{Binding ReportData.DocumentationImage4, Converter={StaticResource NullToVisibilityConverter}}"/>

                                            <!-- أيقونة افتراضية إذا لم تكن الصورة متوفرة -->
                                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center"
                                                  Visibility="{Binding ReportData.DocumentationImage4, ConverterParameter=Inverse, Converter={StaticResource NullToVisibilityConverter}}">
                                                <TextBlock Text="📷" FontSize="48" HorizontalAlignment="Center" Foreground="#6C757D"/>
                                                <TextBlock Text="لا توجد صورة" FontSize="12" HorizontalAlignment="Center" Foreground="#6C757D" Margin="0,5,0,0"/>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </Border>
                            </Grid>

                            <!-- رقم الصفحة الثالثة -->

                        </StackPanel>
                    </Border>

                    <!-- فاصل بين الصفحات -->
                    <Border Height="30" Background="Transparent" Margin="0,10,0,10"/>
                    <Border Style="{StaticResource PrintPageStyle}" Height="1069" Width="732">
                        <Grid>
                            <!-- محتوى العقد -->
                            <StackPanel Margin="2,-12,-12,3" TextElement.FontFamily="Arial">

                                <!-- مقدمة العقد مع البيانات الحقيقية -->

                                <!-- إضافة النص المطلوب -->

                                <!-- رأس العقد المبسط -->
                                <Grid Margin="0,0,0,25">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="2*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- شعار المؤسسة بالشمال -->
                                    <StackPanel Grid.Column="0" HorizontalAlignment="Left" VerticalAlignment="Center">
                                        <Image Source="C:\Users\<USER>\Desktop\sys\icons\sfd.png"
                                   Width="132" Height="64"
                                   HorizontalAlignment="Center"/>
                                    </StackPanel>

                                    <!-- عنوان العقد بالوسط -->
                                    <StackPanel Grid.Column="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <TextBlock Text="عقد اتفاق" FontSize="26" FontWeight="Bold" HorizontalAlignment="Center"
                                     Foreground="Black" Margin="0,0,0,5" FontFamily="Arial"/>
                                        <TextBlock FontSize="24" FontWeight="Bold" HorizontalAlignment="Center"
                                     Foreground="Black" FontFamily="Arial"><Run Text="إيج"/><Run FlowDirection="RightToLeft" Text="ــ"/><Run FlowDirection="RightToLeft" Text="ــــــــ"/><Run FlowDirection="RightToLeft" Text="ـ"/><Run Text="ار "/><Run Language="ar-ye" Text="سيـــــــــارة"/></TextBlock>
                                        <!-- خط تحت العنوان -->
                                        <Border Height="2" Background="Black" Width="200" HorizontalAlignment="Center" Margin="0,8,0,0"/>
                                    </StackPanel>

                                    <!-- رقم العقد ورقم الزيارة باليمين -->
                                    <StackPanel Grid.Column="2" HorizontalAlignment="Right" VerticalAlignment="Center">
                                        <Border Background="#F0FFF0" BorderBrush="#228B22" BorderThickness="1" Padding="8,4">
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                <TextBlock Text="🔢" FontSize="12" Margin="0,0,5,0" VerticalAlignment="Center"/>
                                                <TextBlock FontSize="10" FontWeight="Bold" Foreground="#333333" VerticalAlignment="Center">
                                            <Run Text="رقم الزيارة: "/>
                                            <Run Text="{Binding ReportData.VisitNumber, FallbackValue=3333}"/>
                                                </TextBlock>
                                            </StackPanel>
                                        </Border>
                                    </StackPanel>
                                </Grid>

                                <TextBlock FontSize="17" TextWrapping="Wrap" LineHeight="20" TextAlignment="Justify" FontWeight="Bold" Foreground="Black" FontStretch="SemiExpanded" Margin="5,5,5,8"><Run Text="أنه في يوم "/><Run Text="{Binding ReportData.StartDateArabic}" FontWeight="Bold"/><Run Text=" الموافق "/><Run Text="{Binding ReportData.DepartureDate, StringFormat=dd/MM/yyyy}" FontWeight="Bold"/><Run Text="بمحافظة ذمار، تم بين كل من:"/></TextBlock>
                                <StackPanel Width="707" Height="102">
                                    <!-- الطرف الأول -->
                                    <StackPanel Margin="0,0,0,8">
                                        <!-- عنوان الطرف الأول -->

                                        <!-- بيانات السائق -->
                                        <!-- بيانات السائق -->
                                        <TextBlock FontSize="17" TextWrapping="Wrap" LineHeight="20" TextAlignment="Justify" Foreground="Black" FontStretch="SemiExpanded" Margin="5,5,5,5"><Run Text="- الأخ/ "/><Run Text="{Binding ReportData.WinnerDriver.DriverName}" FontWeight="Bold"/><Run Text=" يحمل بطاقة شخصية رقم ("/><Run Text="{Binding ReportData.WinnerDriver.NationalId}" FontWeight="Bold"/><Run Text=") صادرة من: "/><Run Text="{Binding ReportData.WinnerDriver.CardIssuePlace}" FontWeight="Bold"/><Run Text=" بتاريخ: "/><Run Text="{Binding ReportData.WinnerDriver.CardIssueDate}" FontWeight="Bold"/><Run Text="، وبهذا العقد هو مالك السيارة وسائقها."/></TextBlock>
                                    </StackPanel>

                                    <!-- الطرف الثاني -->
                                    <StackPanel Margin="0,0,0,15" Height="48" Width="709">
                                        <TextBlock FontSize="17" TextWrapping="WrapWithOverflow" LineHeight="20" TextAlignment="Justify" Margin="2,2,2,2" Foreground="Black" FontStretch="SemiExpanded" FontWeight="Bold" Height="46" Width="690"><Run Text="- "/><Run FlowDirection="RightToLeft" Language="ar-ye" Text="الصنـــدوق الاجتمـــاعـــي للتنميــــه فــــرع ذمــــار "/><Run Text="خط صنعاء تعز - جولة كمران - عمارة الأوقاف،ويمثله في هذا العقد الأستاذ/ محمد محمد عبدالجليل الديلمي بصفته مدير الفرع ويسمى في هذا العقد الطرف الثاني."/></TextBlock>
                                    </StackPanel>

                                </StackPanel>

                                <!-- أطراف العقد -->
                                <StackPanel>
                                    <!-- نص الاتفاق -->
                                    <TextBlock Text="الاتفاق والتراضي على ما يلي:" FontSize="18" Foreground="Black" FontStretch="SemiExpanded" FontWeight="Bold" Height="28" Width="715"/>
                                    <!-- نص الاتفاق -->
                                    <!-- نص الاتفاق -->
                                    <!-- نص الاتفاق -->
                                    <!-- نص الاتفاق -->
                                    <!-- نص الاتفاق -->
                                    <!-- نص الاتفاق -->
                                    <!-- نص الاتفاق -->
                                </StackPanel>

                                <!-- أولاً: مواصفات السيارة -->
                                <StackPanel Margin="0,15,0,8" Height="79">
                                    <!-- عنوان مواصفات السيارة -->

                                    <!-- عنوان مواصفات السيارة -->
                                    <Border Background="#F5F5F5" Padding="8,4" Height="36">
                                        <TextBlock FontSize="20" FontWeight="Bold" Foreground="#333333" FontStretch="SemiExpanded"><Run FlowDirection="RightToLeft" Language="ar-ye" Text="أولاً: مواصفات السيارة:"/></TextBlock>
                                    </Border>

                                    <TextBlock FontSize="17" TextWrapping="Wrap" LineHeight="20" TextAlignment="Justify" Margin="5,5,5,5" Foreground="Black" FontStretch="SemiExpanded">
                                <Run Text="أجر الطرف الأول للطرف الثاني سيارة "/>
                                <Run Text="{Binding ReportData.WinnerDriver.VehicleType}" FontWeight="Bold"/>
                                <Run Text=" موديل "/>
                                <Run Text="{Binding ReportData.WinnerDriver.ManufactureYear}" FontWeight="Bold"/>
                                <Run Text=" لون "/>
                                <Run Text="{Binding ReportData.WinnerDriver.VehicleColor}" FontWeight="Bold"/>
                                <Run Text=" برقم ("/>
                                <Run Text="{Binding ReportData.WinnerDriver.VehicleNumber}" FontWeight="Bold"/>
                                <Run Text=") المملوك له بموجب رخصة تسيير مركبة خصوصي رقم "/>
                                <Run Text="{Binding ReportData.WinnerDriver.LicenseNumber}" FontWeight="Bold"/>
                                <Run Text=" الصادرة بتاريخ "/>
                                <Run Text="{Binding ReportData.WinnerDriver.LicenseIssueDate, StringFormat=dd/MM/yyyy}" FontWeight="Bold"/>
                                <Run Text=". ويسمى فيما بعد وسيلة النقل بموجب أحكام وشروط هذا العقد."/>
                                    </TextBlock>
                                </StackPanel>

                                <!-- ثانياً: غرض الانتفاع -->
                                <StackPanel Margin="0,5,0,8">
                                    <!-- عنوان غرض الانتفاع -->
                                    <Border Background="#F5F5F5" Padding="8,4" Margin="0,0,0,3">
                                        <TextBlock FontSize="20" FontWeight="Bold" Foreground="#333333" FontStretch="SemiExpanded"><Run FlowDirection="RightToLeft" Text="ثانياً : غرض الانتفاع"/><Run FlowDirection="RightToLeft" Language="ar-ye" Text=" :"/></TextBlock>
                                    </Border>

                                    <TextBlock FontSize="17" TextWrapping="Wrap" LineHeight="20" TextAlignment="Justify" Margin="5,5,5,5" Foreground="Black" FontStretch="SemiExpanded">
                                <Run Text="اتفق الطرفان على استئجار الطرف الثاني وسيلة نقل (سيارة مع سائقها) "/>
                                <Run Text="{Binding ReportData.VisitConductorPrefix}"/>
                                <Run Text=" "/>
                                <Run Text="{Binding ReportData.VisitConductorWithRank}" FontWeight="Bold"/>
                                <Run Text="{Binding ReportData.VisitConductorSuffix}"/>
                                    </TextBlock>
                                </StackPanel>

                                <!-- ثالثاً: المدة الإيجارية -->
                                <StackPanel Margin="0,5,0,8">
                                    <!-- عنوان المدة الإيجارية -->
                                    <Border Background="#F5F5F5" Padding="8,4" Margin="0,0,0,3">
                                        <TextBlock FontSize="20" FontWeight="Bold" Foreground="#333333" FontStretch="SemiExpanded"><Run FlowDirection="RightToLeft" Language="ar-ye" Text="ثالثاً : المدة الإيجارية:"/></TextBlock>
                                    </Border>

                                    <TextBlock FontSize="17" TextWrapping="Wrap" LineHeight="20" TextAlignment="Justify" Margin="5,5,5,5" Foreground="Black" FontStretch="SemiExpanded">
                                <Run Text="اتفق الطرفان على أن المدة الإيجارية لتنفيذ المهمة تبدأ من يوم "/>
                                <Run Text="{Binding ReportData.StartDateArabic}" FontWeight="Bold"/>
                                <Run Text=" الموافق "/>
                                <Run Text="{Binding ReportData.DepartureDate}" FontWeight="Bold"/>
                                <Run Text=""/>
                                <Run Text="{Binding ReportData.RentalDurationText}" FontWeight="Bold"/>
                                <Run Text=" من تاريخ أول يوم سفر، وتنتهي يوم "/>
                                <Run Text="{Binding ReportData.EndDateArabic}" FontWeight="Bold"/>
                                <Run Text=" الموافق "/>
                                <Run Text="{Binding ReportData.ReturnDate}" FontWeight="Bold"/>
                                <Run Text="، وتسير بموجب وثيقة خطة تنفيذ المهمة المرفقة بالعقد."/>
                                    </TextBlock>
                                </StackPanel>

                                <!-- رابعاً: القيمة الإيجارية -->
                                <StackPanel Margin="0,5,0,8">
                                    <!-- عنوان القيمة الإيجارية -->
                                    <Border Background="#F5F5F5" Padding="8,4" Margin="0,0,0,3">
                                        <TextBlock FontSize="20" FontWeight="Bold" Foreground="#333333" FontStretch="SemiExpanded"><Run FlowDirection="RightToLeft" Language="ar-ye" Text="رابعاً : القيمة الإيجارية (الإجارة):"/></TextBlock>
                                    </Border>

                                    <TextBlock FontSize="17"
               TextWrapping="Wrap"
               LineHeight="20"
               TextAlignment="Justify"
               Margin="5"
               Foreground="Black"
               FontStretch="SemiExpanded" >

        <Run Text="اتفق الطرفان على أن القيمة الإيجارية لوسيلة النقل خلال فترة تنفيذ المهمة بإكملها مبلغ وقدره (" />

        <!-- الرقم مع فواصل الآلاف ودون كسور -->
        <Run Text="{Binding ReportData.WinnerDriver.WinningPrice,
                            StringFormat='{}{0:#,##0}'}"
             FontWeight="Bold" />

        <Run Text=") " />

        <!-- المبلغ بالحروف -->
        <Run Text="{Binding ReportData.WinnerPriceInArabicText}"
             FontWeight="Bold" />

        <Run Text=" ريال يمني، وتدفع من الطرف الثاني للطرف الأول وفقاً للإجراءات المالية للصندوق وتصرف بحسب عدد الأيام الفعلية المنجزة." />
                                    </TextBlock>
                                </StackPanel>
                                <!-- خامساً: إقرار الملكية -->
                                <StackPanel Margin="0,5,0,8">
                                    <!-- عنوان إقرار الملكية -->
                                    <Border Background="#F5F5F5" Padding="8,4" Margin="0,0,0,3">
                                        <TextBlock FontSize="20" FontWeight="Bold" Foreground="#333333" FontStretch="SemiExpanded"><Run FlowDirection="RightToLeft" Language="ar-ye" Text="خامساً:"/></TextBlock>
                                    </Border>

                                    <TextBlock FontSize="17" TextWrapping="Wrap" LineHeight="20" TextAlignment="Justify" Margin="5,5,5,5" Foreground="Black" FontStretch="SemiExpanded"><Run Text="يقر الطرف الأول بموجب هذا العقد بأنه المالك الشرعي لوسيلة النقل، وأنه حائز على وثائق إثبات الملكية الشرعية سارية المفعول وأنها خالية من أي التزامات شخصية أو عينية أو مطالبات أو حقوق للغير."/></TextBlock>
                                </StackPanel>


                                <!-- سادساً: التزامات الطرف الأول -->
                                <StackPanel Margin="0,5,0,8">
                                    <!-- عنوان التزامات الطرف الأول -->
                                    <Border Background="#F5F5F5" Padding="8,4" Margin="0,0,0,3">
                                        <TextBlock FontSize="20" FontWeight="Bold" Foreground="#333333" FontStretch="SemiExpanded"><Run FlowDirection="RightToLeft" Text="سادساً : يلتزم الطرف الأول بموجب هذا "/><Run FlowDirection="RightToLeft" Language="ar-ye" Text="القيام"/><Run FlowDirection="RightToLeft" Text=" بما يلي:"/></TextBlock>
                                    </Border>

                                    <TextBlock FontSize="17" TextWrapping="Wrap" LineHeight="9" TextAlignment="Justify" Margin="5,5,5,5" Foreground="Black" FontStretch="SemiExpanded" Height="245"><Run Text="• ينفذ العقد بنفسه وأن يبذل في تأديته من العناية مايبذله الشخص المعتاد أو من يمثله بتفويض رسمي."/><LineBreak/><LineBreak/><Run Text="• الامتثال بأخلاقيات ومدونة سلوك الصندوق الاجتماعي للتنمية أثناء الرحلة."/><LineBreak/><LineBreak/><Run Text="• الحرص على سلامة الركاب والالتزام بكافة قواعد وآداب المرور وقانونه منها:"/><LineBreak/><LineBreak/><Run Text="    ★ يجب على السائق وكافة الركاب ربط حزام الأمان في كل الأوقات أثناء حركة المركبة ولا يسمح للراكب الجلوس بدونه."/><LineBreak/><Run Text="    "/><LineBreak/><Run Text="     "/><Run Text="★ يمنع على السائقين استخدام الهاتف المحمول أثناء القيادة."/></TextBlock>

                                    <!-- توقيعات تحت النص -->
                                    <StackPanel Margin="0,30,0,0"/>
                                </StackPanel>



                                <!-- رقم الصفحة الرابعة -->

                            </StackPanel>

                            <!-- Header ثابت للطرفين - موقع مطلق في أسفل الصفحة -->
                            <Border Background="White" BorderBrush="#FFF7F8F9" BorderThickness="1"
                            Width="650" Height="70"
                            HorizontalAlignment="Center" VerticalAlignment="Top"
                            Margin="0,1009,0,0" Padding="10">
                                <Grid Margin="0,-11,0,-11">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- الطرف الأول -->
                                    <StackPanel Grid.Column="0" HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <TextBlock Text="{Binding ReportData.WinnerDriver.DriverName}"
                                         HorizontalAlignment="Center" FontSize="14" FontWeight="Bold"
                                         Foreground="Black" Margin="0,0,0,2"/>
                                        <TextBlock Text="الطرف الأول"
                                         HorizontalAlignment="Center" FontSize="12"
                                         Foreground="#666666" FontWeight="Bold"/>
                                    </StackPanel>

                                    <!-- الطرف الثاني -->
                                    <StackPanel Grid.Column="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <TextBlock Text="{Binding ReportData.VisitConductor}"
                                         HorizontalAlignment="Center" FontSize="14" FontWeight="Bold"
                                         Foreground="Black" TextWrapping="Wrap" TextAlignment="Center"
                                         MaxWidth="200" Margin="0,0,0,2"/>
                                        <TextBlock Text="الطرف الثاني"
                                         HorizontalAlignment="Center" FontSize="12"
                                         Foreground="#666666" FontWeight="Bold"/>
                                    </StackPanel>
                                </Grid>
                            </Border>
                        </Grid>
                    </Border>

                    <!-- الصفحة الثانية: عقد إيجار السيارة (الجزء الأول) -->

                    <!-- فاصل بين الصفحات -->
                    <Border Height="21" Background="Transparent" Margin="0,10,0,10"/>

                    <!-- الصفحة الثالثة: عقد إيجار السيارة (الجزء الثاني) -->
                    <Border Style="{StaticResource PrintPageStyle}" Margin="5,10,5,10" Height="1219" Width="734">
                        <StackPanel Margin="20" TextElement.FontFamily="Arial">

                            <!-- رأس الصفحة الثالثة -->
                            <Grid Margin="0,0,0,20">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="2*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- شعار المؤسسة بالشمال -->
                                <StackPanel Grid.Column="0" HorizontalAlignment="Left" VerticalAlignment="Center">
                                    <Image Source="C:\Users\<USER>\Desktop\sys\icons\sfd.png"
                                   Width="110" Height="86"
                                   HorizontalAlignment="Center"/>
                                </StackPanel>

                                <!-- عنوان العقد بالوسط -->
                                <StackPanel Grid.Column="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <TextBlock Text="تابع عقد اتفاق" FontSize="26" FontWeight="Bold" HorizontalAlignment="Center"
                                     Foreground="Black" Margin="0,0,0,5" FontFamily="Arial"/>
                                    <TextBlock Text="إيجار سيارة" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center"
                                     Foreground="Black" FontFamily="Arial"/>
                                    <!-- خط تحت العنوان -->
                                    <Border Height="2" Background="Black" Width="200" HorizontalAlignment="Center" Margin="0,8,0,0"/>
                                </StackPanel>

                                <!-- رقم العقد ورقم الزيارة باليمين -->
                                <StackPanel Grid.Column="2" HorizontalAlignment="Right" VerticalAlignment="Center">
                                    <Border Background="#F0FFF0" BorderBrush="#228B22" BorderThickness="1" Padding="8,4">
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <TextBlock Text="🔢" FontSize="12" Margin="0,0,5,0" VerticalAlignment="Center"/>
                                            <TextBlock FontSize="10" FontWeight="Bold" Foreground="#333333" VerticalAlignment="Center">
                                            <Run Text="رقم الزيارة: "/>
                                            <Run Text="{Binding ReportData.VisitNumber, FallbackValue=3333}"/>
                                            </TextBlock>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </Grid>

                            <!-- إضافة النقاط الإضافية لسادساً -->
                            <StackPanel Margin="0,0,0,8">
                                <TextBlock FontSize="16" TextWrapping="Wrap" LineHeight="14" TextAlignment="Justify" Margin="5,5,5,5" Foreground="Black" FontStretch="SemiExpanded" FontWeight="Bold"><Run Text="    ★ الإلتزام بحدود السرعة في الطرق العامة، ولكن لا يسمح بتجاوز السرعة تحت أي ظرف من الظروف كما يلي:"/><LineBreak/><LineBreak/><Run Text="        - 100 كم/ الساعة على الطرق العامة المسفلتة كحد أقصى."/><LineBreak/><Run/><LineBreak/><Run Text="        - 60 كم/ الساعة على الطرق الغير مسفلتة والتربية كحد أقصى."/><LineBreak/><Run/><LineBreak/><Run Text="        - 40 كم/ الساعة على الطرق داخل القرى وفي الأحياء السكنية كحد أقصى."/><LineBreak/><LineBreak/><Run Text="• عدم نقل أي ركاب آخرين عدى الطرف الثاني أثناء أداء المهام الميدانية."/><LineBreak/><LineBreak/><Run Text="• اصطحاب كافة وثائق الملكية أثناء تنفيذ المهمة."/><LineBreak/><LineBreak/><Run Text="• سرعة الإبلاغ عن أي حوادث تحدث أثناء قيادة المركبة خلال ساعات العمل أو خارجها."/><LineBreak/><LineBreak/><Run Text="• اصطحاب أدوات السلامة مثل طفاية الحريق (نوع بودر حجم 1كيلو) - صندوق إسعافات أولية - مثلث التحذير - عدة صيانة السيارة مثل &quot;إطار احتياطي .... وغيرها&quot;."/></TextBlock>
                            </StackPanel>

                            <!-- سابعاً: التزامات الطرف الثاني -->
                            <StackPanel Margin="0,0,0,5">
                                <!-- عنوان التزامات الطرف الثاني -->
                                <Border Background="#F5F5F5" Padding="6,3" Margin="0,0,0,2">
                                    <TextBlock FontSize="18" FontWeight="Bold" Foreground="#333333"><Run FlowDirection="RightToLeft" Language="ar-ye" Text="سابعاً:"/></TextBlock>
                                </Border>

                                <TextBlock FontSize="15" TextWrapping="Wrap" LineHeight="25" TextAlignment="Justify" Foreground="Black" Margin="5,0,5,5" FontWeight="Bold"><Run Text="يقر الطرف الثاني بأنه عاين كافة الوثائق المطلوبة في وسيلة النقل ووجد أنها مستوفية لكافة لوازمها ولذلك يلتزم بموجب هذا العقد بما يلي:"/></TextBlock>

                                <!-- Obligation Items -->
                                <StackPanel Margin="15,0,0,0">
                                    <TextBlock FontSize="15" TextWrapping="Wrap" LineHeight="25" TextAlignment="Justify" Foreground="Black" Margin="0,0,0,3"><Run Text="أ" FontWeight="Bold"/><Run Text=" "/><Run Text="   التزام ممثليه بتعليمات سائق وسيلة النقل."/></TextBlock>

                                    <TextBlock FontSize="15" TextWrapping="Wrap" LineHeight="25" TextAlignment="Justify" Foreground="Black" Margin="0,0,0,3"><Run Text="ب" FontWeight="Bold"/><Run Text=" "/><Run Text="   التزام ممثليه بوقت وموعد الرحلة."/></TextBlock>

                                    <TextBlock FontSize="15" TextWrapping="Wrap" LineHeight="25" TextAlignment="Justify" Foreground="Black"><Run Text="ت" FontWeight="Bold"/><Run Text=" "/><Run Text="   تبليغ السائق على الفور عند إلغاء أو قطع أو تمديد مهمة العمل بدون إنذار مسبق."/></TextBlock>
                                </StackPanel>
                            </StackPanel>

                            <!-- ثامناً: تسوية وحل المنازعات -->
                            <StackPanel Margin="0,0,0,5">
                                <!-- عنوان تسوية وحل المنازعات -->
                                <Border Background="#F5F5F5" Padding="6,3" Margin="0,0,0,2">
                                    <TextBlock FontSize="18" FontWeight="Bold" Foreground="#333333"><Run FlowDirection="RightToLeft" Language="ar-ye" Text="ثامناً: تسوية وحل المنازعات وطرف الفصل فيها:"/></TextBlock>
                                </Border>

                                <TextBlock FontSize="15" TextWrapping="Wrap" LineHeight="25" TextAlignment="Justify" Foreground="Black" Margin="5,0,5,0"><Run Text="كل نزاع ينشأ بين الطرفين أو خلفهما يتعلق بانعقاد أو تنفيذ أو تفسير هذا العقد، أو ما يتفرع عنه أو يرتبط به بأي وجه من الوجوه يتم حله وتسويته بينهما أولاً بالطرق الودية خلال "/><Run Text=" "/><Run Text="تسعين يوماً" FontWeight="Bold"/><Run Text=" "/><Run Text="."/></TextBlock>
                            </StackPanel>

                            <!-- تاسعاً: أحكام أخرى -->
                            <StackPanel Margin="0,0,0,5">
                                <!-- عنوان أحكام أخرى -->
                                <Border Background="#F5F5F5" Padding="6,3" Margin="0,0,0,2">
                                    <TextBlock FontSize="18" FontWeight="Bold" Foreground="#333333"><Run FlowDirection="RightToLeft" Language="ar-ye" Text="تاسعاً: أحكام أخرى:"/></TextBlock>
                                </Border>

                                <!-- Provision Items -->
                                <StackPanel Margin="15,0,0,0">
                                    <TextBlock FontSize="15" TextWrapping="Wrap" LineHeight="25" TextAlignment="Justify" Foreground="Black" Margin="0,0,0,3"><Run Text="⏰"/><Run Text=" ينتهي العقد بانتهاء مدته المتفق عليها في العقد دون الحاجة إلى تنبيه أو إنذار ما لم يبلغ الطرف الثاني الطرف الأول برغبته في تمديد "/><Run Text="المدة."/></TextBlock>

                                    <TextBlock FontSize="15" TextWrapping="Wrap" LineHeight="25" TextAlignment="Justify" Foreground="Black" Margin="0,0,0,3"><Run Text="🛡️"/><Run Text=" الطرف الأول (مالك السيارة) المسؤول "/><Run Text="وحده"/><Run Text=" "/><Run Text="علـى "/><Run Text="التأمين على نفسه وعلى سيارته من كافة الحوادث والاعتداءات من الغير، ولا "/><LineBreak/><Run Text="      "/><Run Text="يتحمل الصندوق أي مسؤولية تجاه ما قد يتعرض له أثناء تنفيذ المهمة."/></TextBlock>

                                    <TextBlock FontSize="15" TextWrapping="Wrap" LineHeight="25" TextAlignment="Justify" Foreground="Black"><Run Text="⚠️"/><Run Text=" يتحمل الطرف الأول(مالك السيارة والسائق)وحده مسئولية تعويض الراكب عن أي ضرر قد يحدث له نتيجة إصابته "/><Run Text="بسـبـب "/><Run Text="وقوع "/><LineBreak/><Run Text="      "/><Run Text="حادث للسيارة."/></TextBlock>
                                </StackPanel>
                            </StackPanel>

                            <!-- خاتمة العقد -->
                            <StackPanel Margin="0,0,0,5">
                                <TextBlock Text="خاتمة العقد" FontWeight="Bold" FontSize="15" Foreground="Black" HorizontalAlignment="Center" Margin="0,0,0,3"/>
                                <TextBlock FontSize="15" TextWrapping="Wrap" LineHeight="16" TextAlignment="Center" Foreground="Black"><Run Text="حرر هذا العقد من "/><Run Text=" "/><Run Text="ثلاث نسخ" FontWeight="Bold"/><Run Text=" "/><Run Text=" بيد كل طرف نسخة منه، والنسخة الثالثة للتوثيق."/></TextBlock>
                            </StackPanel>

                            <TextBlock FontSize="15" FontWeight="Bold" TextAlignment="Center" Foreground="Black" Margin="0,0,0,8"><Run Text="🤲 ولله الأمر كله وهو على كل شيء شهيد 🤲"/></TextBlock>

                            <!-- توقيعات أطراف العقد -->
                            <StackPanel Margin="0,0,0,20">
                                <Grid Height="115">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- First Party Signature -->
                                    <StackPanel Grid.Column="0" HorizontalAlignment="Left" Margin="36,10,0,-18" Width="239">
                                        <TextBlock Text="الطرف الأول" FontWeight="Bold" FontSize="18" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,8"/>
                                        <TextBlock Text="(مالك السيارة)" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" Foreground="#666666" Margin="0,0,0,20"/>
                                        <TextBlock Text="{Binding ReportData.WinnerDriver.DriverName}" HorizontalAlignment="Center" FontSize="16" FontWeight="Bold" Foreground="Black" Margin="0,0,0,12"/>
                                        <TextBlock Text="{Binding ReportData.WinnerDriver.PhoneNumber}" HorizontalAlignment="Center" FontSize="14" Foreground="#666666"/>
                                    </StackPanel>

                                    <!-- Second Party Signature -->
                                    <StackPanel Grid.Column="1" HorizontalAlignment="Center" Margin="0,10,0,2" Width="313">
                                        <TextBlock Text="الطرف الثاني" FontWeight="Bold" FontSize="18" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,8"/>
                                        <TextBlock Text="القائم بالمهمة" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" Foreground="#666666" Margin="0,0,0,20"/>
                                        <TextBlock Text="{Binding ReportData.VisitConductor}" HorizontalAlignment="Center" FontSize="16" FontWeight="Bold"
                                             Foreground="Black" Margin="0,0,0,12" TextWrapping="Wrap" TextAlignment="Center" MaxWidth="300"/>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                            <StackPanel Height="64" Width="647" 

                        >

                                <TextBlock Text="يعتمد مدير الفرع" FontWeight="Bold" FontSize="18" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,20"/>
                                <TextBlock Text="م/محمد محمد الديلمي" HorizontalAlignment="Center" FontSize="16" FontWeight="Bold" Foreground="Black" Margin="0,0,0,10"/>
                            </StackPanel>

                            <!-- اعتماد مدير الفرع -->

                            <!-- رقم الصفحة الخامسة -->
                            <Border Background="Transparent" Padding="0,20,0,20" HorizontalAlignment="Left"/>

                        </StackPanel>
                    </Border>

                    <!-- فاصل بين الصفحات -->
                    <Border Height="30" Background="Transparent" Margin="0,10,0,10"/>

                    <!-- الصفحة الرابعة: بيان وإقرار -->
                    <Border Style="{StaticResource PrintPageStyle}" Margin="5,10,5,10" Height="1227" Width="736">
                        <StackPanel Margin="25,15,25,15" TextElement.FontFamily="Arial">

                            <!-- رأس الصفحة الرابعة -->
                            <Grid Margin="0,0,0,15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="150"/>
                                </Grid.ColumnDefinitions>

                                <!-- شعار المؤسسة بالشمال -->
                                <StackPanel Grid.Column="0" HorizontalAlignment="Left" VerticalAlignment="Center">
                                    <Border Background="White" BorderBrush="Black" Padding="5">
                                        <Image Source="C:\Users\<USER>\Desktop\sys\icons\sfd.png"
                                       Width="80" Height="80"
                                       HorizontalAlignment="Center"/>
                                    </Border>
                                </StackPanel>

                                <!-- عنوان العقد بالوسط -->
                                <StackPanel Grid.Column="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <Border Background="White" BorderBrush="Black" BorderThickness="0,0,0,1" Padding="15,10" Margin="10,0" Width="251">
                                        <StackPanel>
                                            <TextBlock Text="بيان وإقرار" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center"
                                             Foreground="Black" Margin="0,0,0,5"/>
                                            <TextBlock Text="تضارب المصالح والعلاقات العائلية" FontSize="16" FontWeight="SemiBold" HorizontalAlignment="Center"
                                             Foreground="Black" TextWrapping="Wrap"/>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>

                                <!-- رقم العقد ورقم الزيارة باليمين -->
                                <StackPanel Grid.Column="2" HorizontalAlignment="Right" VerticalAlignment="Center">
                                    <Border Background="#F0FFF0" BorderBrush="#228B22" BorderThickness="1" Padding="8,4">
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <TextBlock Text="🔢" FontSize="12" Margin="0,0,5,0" VerticalAlignment="Center"/>
                                            <TextBlock FontSize="10" FontWeight="Bold" Foreground="#333333" VerticalAlignment="Center">
                                            <Run Text="رقم الزيارة: "/>
                                            <Run Text="{Binding ReportData.VisitNumber, FallbackValue=3333}"/>
                                            </TextBlock>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </Grid>

                            <!-- إقرار مزود الخدمة -->
                            <StackPanel Margin="0,0,0,10">
                                <Border Background="#FFF9F9F9" BorderBrush="Black" BorderThickness="2" Padding="10,8" Margin="0,0,0,8" Width="196">
                                    <TextBlock Text="إقرار مزود الخدمة" FontWeight="Bold" FontSize="18" Foreground="Black" HorizontalAlignment="Center"/>
                                </Border>

                                <Border Background="White" BorderBrush="Transparent" BorderThickness="1" Padding="15,10">
                                    <TextBlock FontSize="14" TextWrapping="Wrap" LineHeight="20" TextAlignment="Justify" Foreground="Black">
                                    <Run Text="أقر أنا مزود الخدمة صاحب المركبة رقم "/>
                                    <Run Text="{Binding ReportData.WinnerDriver.VehicleNumber}" FontWeight="Bold"/>
                                    <Run Text=" بأنني تسلمت من "/>
                                    <Run Text="الصندوق الاجتماعي للتنمية" FontWeight="Bold"/>
                                    <Run Text=" نسخة من "/>
                                    <Run Text="مدونة تضارب المصالح" FontWeight="Bold"/>
                                    <Run Text="، وبخصوص تعاقدي مع العاملين في الصندوق فإنني أقر بما يلي:"/>
                                    </TextBlock>
                                </Border>
                            </StackPanel>

                            <!-- القسم الأول: العلاقات العائلية -->
                            <StackPanel Margin="0,0,0,15">
                                <Border Background="#FFF9F9F9" BorderBrush="Black" BorderThickness="2" Padding="10,6" Margin="0,0,0,8" Width="199">
                                    <TextBlock FontWeight="Bold" FontSize="16" Foreground="Black" HorizontalAlignment="Center" Text="العلاقات العائلية"/>
                                </Border>

                                <!-- سؤال العلاقة العائلية -->
                                <Border Background="White" BorderBrush="Transparent" BorderThickness="1" Padding="12,8" Margin="0,0,0,8">
                                    <StackPanel>
                                        <TextBlock FontSize="14" FontWeight="SemiBold" Foreground="Black" Margin="0,0,0,6">
                                        <Run Text="1) هل لديك علاقة عائلية مع أحد العاملين في الصندوق؟"/>
                                        </TextBlock>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <Border Background="White" BorderBrush="Black" BorderThickness="1" Padding="12,6" Margin="0,0,15,0">
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="☐" FontSize="14" Margin="0,0,6,0" Foreground="Black"/>
                                                    <TextBlock Text="نعم" FontSize="12" FontWeight="Bold" Foreground="Black"/>
                                                </StackPanel>
                                            </Border>
                                            <Border Background="White" BorderBrush="Black" BorderThickness="1" Padding="12,6">
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="☐" FontSize="14" Margin="0,0,6,0" Foreground="Black"/>
                                                    <TextBlock Text="لا" FontSize="12" FontWeight="Bold" Foreground="Black"/>
                                                </StackPanel>
                                            </Border>
                                        </StackPanel>
                                    </StackPanel>
                                </Border>

                                <TextBlock Text="في حال كانت الإجابة نعم يتم تعبئة ما يلي:" FontSize="12" FontWeight="Bold" Foreground="Black" HorizontalAlignment="Center" Margin="0,0,0,8"/>

                                <!-- جدول تفاصيل الموظفين -->
                                <Border Background="White" BorderBrush="Black" BorderThickness="1" Padding="8">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- رؤوس الجدول -->
                                        <Border Grid.Row="0" Grid.Column="0" Background="#FFF0F0F0" Padding="8,6" Margin="0">
                                            <TextBlock Text="اسم العامل" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" Foreground="Black"/>
                                        </Border>
                                        <Border Grid.Row="0" Grid.Column="1" Background="#FFF0F0F0" Padding="8,6" Margin="0">
                                            <TextBlock Text="فرع الصندوق" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" Foreground="Black"/>
                                        </Border>
                                        <Border Grid.Row="0" Grid.Column="2" Background="#FFF0F0F0" Padding="8,6" Margin="0">
                                            <TextBlock Text="نوع العلاقة العائلية" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" Foreground="Black"/>
                                        </Border>

                                        <!-- صفوف البيانات -->
                                        <Border Grid.Row="1" Grid.Column="0" Background="White" BorderBrush="Black" BorderThickness="1" Padding="8,10" Margin="0">
                                            <TextBlock Text=" " HorizontalAlignment="Center" FontSize="10" Foreground="Black"/>
                                        </Border>
                                        <Border Grid.Row="1" Grid.Column="1" Background="White" BorderBrush="Black" BorderThickness="1" Padding="8,10" Margin="0">
                                            <TextBlock Text="" HorizontalAlignment="Center" FontSize="10" Foreground="Black"/>
                                        </Border>
                                        <Border Grid.Row="1" Grid.Column="2" Background="White" BorderBrush="Black" BorderThickness="1" Padding="8,10" Margin="0">
                                            <TextBlock Text="" HorizontalAlignment="Center" FontSize="10" Foreground="Black"/>
                                        </Border>

                                        <Border Grid.Row="2" Grid.Column="0" Background="White" BorderBrush="Black" BorderThickness="1" Padding="8,10" Margin="0">
                                            <TextBlock Text="" HorizontalAlignment="Center" FontSize="10" Foreground="Black"/>
                                        </Border>
                                        <Border Grid.Row="2" Grid.Column="1" Background="White" BorderBrush="Black" BorderThickness="1" Padding="8,10" Margin="0">
                                            <TextBlock Text="" HorizontalAlignment="Center" FontSize="10" Foreground="Black"/>
                                        </Border>
                                        <Border Grid.Row="2" Grid.Column="2" Background="White" BorderBrush="Black" BorderThickness="1" Padding="8,10" Margin="0">
                                            <TextBlock Text="" HorizontalAlignment="Center" FontSize="10" Foreground="Black"/>
                                        </Border>

                                        <Border Grid.Row="3" Grid.Column="0" Background="White" BorderBrush="Black" BorderThickness="1" Padding="8,10" Margin="0">
                                            <TextBlock Text="" HorizontalAlignment="Center" FontSize="10" Foreground="Black"/>
                                        </Border>
                                        <Border Grid.Row="3" Grid.Column="1" Background="White" BorderBrush="Black" BorderThickness="1" Padding="8,10" Margin="0">
                                            <TextBlock Text="" HorizontalAlignment="Center" FontSize="10" Foreground="Black"/>
                                        </Border>
                                        <Border Grid.Row="3" Grid.Column="2" Background="White" BorderBrush="Black" BorderThickness="1" Padding="8,10" Margin="0">
                                            <TextBlock Text="" HorizontalAlignment="Center" FontSize="10" Foreground="Black"/>
                                        </Border>
                                    </Grid>
                                </Border>
                            </StackPanel>

                            <!-- القسم الثاني: المصالح التجارية -->
                            <StackPanel Margin="0,0,0,15">

                                <!-- سؤال المصالح التجارية -->
                                <Border Background="White" BorderBrush="Transparent" BorderThickness="1" Padding="12,8" Margin="0,0,0,8">
                                    <StackPanel>
                                        <TextBlock FontSize="14" FontWeight="SemiBold" Foreground="Black" Margin="0,0,0,6">
                                        <Run Text="2) هل لديك مصالح مباشرة أو غير مباشرة مع أحد العاملين في الصندوق؟"/>
                                        </TextBlock>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <Border Background="White" BorderBrush="Black" BorderThickness="1" Padding="12,6" Margin="0,0,15,0">
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="☐" FontSize="14" Margin="0,0,6,0" Foreground="Black"/>
                                                    <TextBlock Text="نعم" FontSize="12" FontWeight="Bold" Foreground="Black"/>
                                                </StackPanel>
                                            </Border>
                                            <Border Background="White" BorderBrush="Black" BorderThickness="1" Padding="12,6">
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="☐" FontSize="14" Margin="0,0,6,0" Foreground="Black"/>
                                                    <TextBlock Text="لا" FontSize="12" FontWeight="Bold" Foreground="Black"/>
                                                </StackPanel>
                                            </Border>
                                        </StackPanel>
                                    </StackPanel>
                                </Border>

                                <TextBlock Text="في حال كانت الإجابة نعم يتم تعبئة ما يلي:" FontSize="12" FontWeight="Bold" Foreground="Black" HorizontalAlignment="Center" Margin="0,0,0,8"/>

                                <!-- جدول المصالح التجارية -->
                                <Border Background="White" BorderBrush="Black" BorderThickness="1" Padding="8" Height="153">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- رؤوس الجدول -->
                                        <Border Grid.Row="0" Grid.Column="0" Background="#FFF0F0F0" Padding="8,6" Margin="0">
                                            <TextBlock Text="اسم العامل" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" Foreground="Black"/>
                                        </Border>
                                        <Border Grid.Row="0" Grid.Column="1" Background="#FFF0F0F0" Padding="8,6" Margin="0">
                                            <TextBlock Text="فرع الصندوق" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" Foreground="Black"/>
                                        </Border>
                                        <Border Grid.Row="0" Grid.Column="2" Background="#FFF0F0F0" Padding="8,6" Margin="0">
                                            <TextBlock Text="نوع المصلحة" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" Foreground="Black"/>
                                        </Border>

                                        <!-- صفوف البيانات -->
                                        <Border Grid.Row="1" Grid.Column="0" Background="White" BorderBrush="Black" BorderThickness="1" Padding="8,10" Margin="0">
                                            <TextBlock Text="" HorizontalAlignment="Center" FontSize="10" Foreground="Black"/>
                                        </Border>
                                        <Border Grid.Row="1" Grid.Column="1" Background="White" BorderBrush="Black" BorderThickness="1" Padding="8,10" Margin="0">
                                            <TextBlock Text="" HorizontalAlignment="Center" FontSize="10" Foreground="Black"/>
                                        </Border>
                                        <Border Grid.Row="1" Grid.Column="2" Background="White" BorderBrush="Black" BorderThickness="1" Padding="8,10" Margin="0">
                                            <TextBlock Text="" HorizontalAlignment="Center" FontSize="10" Foreground="Black"/>
                                        </Border>

                                        <Border Grid.Row="2" Grid.Column="0" Background="White" BorderBrush="Black" BorderThickness="1" Padding="8,10" Margin="0">
                                            <TextBlock Text="" HorizontalAlignment="Center" FontSize="10" Foreground="Black"/>
                                        </Border>
                                        <Border Grid.Row="2" Grid.Column="1" Background="White" BorderBrush="Black" BorderThickness="1" Padding="8,10" Margin="0">
                                            <TextBlock Text="" HorizontalAlignment="Center" FontSize="10" Foreground="Black"/>
                                        </Border>
                                        <Border Grid.Row="2" Grid.Column="2" Background="White" BorderBrush="Black" BorderThickness="1" Padding="8,10" Margin="0">
                                            <TextBlock Text="" HorizontalAlignment="Center" FontSize="10" Foreground="Black"/>
                                        </Border>

                                        <Border Grid.Row="3" Grid.Column="0" Background="White" BorderBrush="Black" BorderThickness="1" Padding="8,10" Margin="0">
                                            <TextBlock Text="" HorizontalAlignment="Center" FontSize="10" Foreground="Black"/>
                                        </Border>
                                        <Border Grid.Row="3" Grid.Column="1" Background="White" BorderBrush="Black" BorderThickness="1" Padding="8,10" Margin="0">
                                            <TextBlock Text="" HorizontalAlignment="Center" FontSize="10" Foreground="Black"/>
                                        </Border>
                                        <Border Grid.Row="3" Grid.Column="2" Background="White" BorderBrush="Black" BorderThickness="1" Padding="8,10" Margin="0">
                                            <TextBlock Text="" HorizontalAlignment="Center" FontSize="10" Foreground="Black"/>
                                        </Border>
                                    </Grid>
                                </Border>
                            </StackPanel>

                            <!-- الإقرار النهائي -->
                            <StackPanel Margin="0,-9,0,15">
                                <Border Background="#FFF9F9F9" BorderBrush="Black" BorderThickness="2" Padding="15,10" Margin="0,0,0,10" Height="42" Width="192">
                                    <TextBlock Text="الإقرار النهائي" FontWeight="Bold" FontSize="16" Foreground="Black" HorizontalAlignment="Center"/>
                                </Border>

                                <Border Background="White" BorderBrush="Black" Padding="15,10" Margin="0,0,0,10">
                                    <TextBlock FontSize="14" TextWrapping="Wrap" LineHeight="20" TextAlignment="Justify" Foreground="Black">
                                    <Run Text="أنا الموقع أدناه أقر بأن جميع البيانات المذكورة أعلاه "/>
                                    <Run Text="صحيحة" FontWeight="Bold"/>
                                    <Run Text="، وبحق للصندوق اتخاذ الإجراءات التي يراها مناسبة تجاهي في حال عدم صحة البيانات المذكورة."/>
                                    </TextBlock>
                                </Border>

                                <!-- عبارة التوفيق -->
                                <Border Background="White" BorderBrush="Black" Padding="10,6" Margin="0,0,0,15">
                                    <TextBlock FontSize="14" FontWeight="Bold" TextAlignment="Center" Foreground="Black" Text="والله الموفق"/>
                                </Border>

                                <!-- قسم التوقيع -->
                                <StackPanel HorizontalAlignment="Right" Margin="0,40,80,40">
                                    <!-- قسم الاسم -->
                                    <StackPanel HorizontalAlignment="Center" Margin="0,0,0,50">
                                        <TextBlock Text="الاسم" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,15"/>
                                        <TextBlock Text="{Binding ReportData.WinnerDriver.DriverName}" FontSize="16" FontWeight="Bold"
                                             HorizontalAlignment="Center" Foreground="Black"/>
                                    </StackPanel>

                                    <!-- قسم التوقيع -->
                                    <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="التوقيع" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Foreground="Black"/>
                                    </StackPanel>
                                </StackPanel>
                            </StackPanel>

                            <!-- رقم الصفحة السادسة -->

                        </StackPanel>
                    </Border>

                    <!-- فاصل بين الصفحات -->



                </StackPanel>
            </ScrollViewer>

        </Grid>

        <!-- هيدر التوقيعات الثابت - خارج Grid الرئيسي -->

    </Grid>
</UserControl>
